import 'dart:io';
import 'package:permission_handler/permission_handler.dart';
import 'package:geolocator/geolocator.dart';

/// Service for handling app permissions
class PermissionService {
  static PermissionService? _instance;
  static PermissionService get instance => _instance ??= PermissionService._();
  PermissionService._();

  /// Check if all required permissions for automatic validation are granted
  Future<AutomaticValidationPermissionStatus> checkAutomaticValidationPermissions() async {
    final results = <PermissionType, PermissionStatus>{};

    // Check location permission
    final locationPermission = await Geolocator.checkPermission();
    results[PermissionType.location] = _mapLocationPermission(locationPermission);

    // Check background location permission (Android only)
    if (Platform.isAndroid) {
      final backgroundLocation = await Permission.locationAlways.status;
      results[PermissionType.backgroundLocation] = backgroundLocation;
    }

    // Check notification permission
    final notification = await Permission.notification.status;
    results[PermissionType.notification] = notification;

    return AutomaticValidationPermissionStatus(
      permissions: results,
      allGranted: results.values.every((status) => status == PermissionStatus.granted),
    );
  }

  /// Request all permissions required for automatic validation
  Future<AutomaticValidationPermissionStatus> requestAutomaticValidationPermissions() async {
    final results = <PermissionType, PermissionStatus>{};

    try {
      // Request location permission first
      LocationPermission locationPermission = await Geolocator.checkPermission();
      if (locationPermission == LocationPermission.denied) {
        locationPermission = await Geolocator.requestPermission();
      }
      results[PermissionType.location] = _mapLocationPermission(locationPermission);

      // Request background location permission if location is granted
      if (locationPermission == LocationPermission.whileInUse || 
          locationPermission == LocationPermission.always) {
        
        if (Platform.isAndroid) {
          final backgroundLocation = await Permission.locationAlways.request();
          results[PermissionType.backgroundLocation] = backgroundLocation;
        } else if (Platform.isIOS) {
          // On iOS, request always permission directly
          if (locationPermission != LocationPermission.always) {
            locationPermission = await Geolocator.requestPermission();
            results[PermissionType.location] = _mapLocationPermission(locationPermission);
          }
          results[PermissionType.backgroundLocation] = 
              locationPermission == LocationPermission.always 
                  ? PermissionStatus.granted 
                  : PermissionStatus.denied;
        }
      }

      // Request notification permission
      final notification = await Permission.notification.request();
      results[PermissionType.notification] = notification;

    } catch (e) {
      // Handle any errors during permission requests
      return AutomaticValidationPermissionStatus(
        permissions: results,
        allGranted: false,
        error: e.toString(),
      );
    }

    return AutomaticValidationPermissionStatus(
      permissions: results,
      allGranted: results.values.every((status) => status == PermissionStatus.granted),
    );
  }

  /// Check a specific permission
  Future<PermissionStatus> checkPermission(PermissionType type) async {
    switch (type) {
      case PermissionType.location:
        final permission = await Geolocator.checkPermission();
        return _mapLocationPermission(permission);
      
      case PermissionType.backgroundLocation:
        if (Platform.isAndroid) {
          return await Permission.locationAlways.status;
        } else {
          final permission = await Geolocator.checkPermission();
          return permission == LocationPermission.always 
              ? PermissionStatus.granted 
              : PermissionStatus.denied;
        }
      
      case PermissionType.notification:
        return await Permission.notification.status;
      
      case PermissionType.camera:
        return await Permission.camera.status;
      
      case PermissionType.storage:
        return await Permission.storage.status;
    }
  }

  /// Request a specific permission
  Future<PermissionStatus> requestPermission(PermissionType type) async {
    switch (type) {
      case PermissionType.location:
        final permission = await Geolocator.requestPermission();
        return _mapLocationPermission(permission);
      
      case PermissionType.backgroundLocation:
        if (Platform.isAndroid) {
          return await Permission.locationAlways.request();
        } else {
          final permission = await Geolocator.requestPermission();
          return permission == LocationPermission.always 
              ? PermissionStatus.granted 
              : PermissionStatus.denied;
        }
      
      case PermissionType.notification:
        return await Permission.notification.request();
      
      case PermissionType.camera:
        return await Permission.camera.request();
      
      case PermissionType.storage:
        return await Permission.storage.request();
    }
  }

  /// Open app settings
  Future<bool> openAppSettings() async {
    return await openAppSettings();
  }

  /// Check if permission is permanently denied
  bool isPermanentlyDenied(PermissionStatus status) {
    return status == PermissionStatus.permanentlyDenied;
  }

  /// Get user-friendly permission description
  String getPermissionDescription(PermissionType type) {
    switch (type) {
      case PermissionType.location:
        return 'Location access is needed to determine your presence in zones.';
      
      case PermissionType.backgroundLocation:
        return 'Background location access is required for automatic zone validation when the app is not active.';
      
      case PermissionType.notification:
        return 'Notifications are used to inform you about zone validation status and important updates.';
      
      case PermissionType.camera:
        return 'Camera access is needed to capture photos for zone verification.';
      
      case PermissionType.storage:
        return 'Storage access is needed to save photos and documents.';
    }
  }

  /// Get permission name for display
  String getPermissionName(PermissionType type) {
    switch (type) {
      case PermissionType.location:
        return 'Location';
      case PermissionType.backgroundLocation:
        return 'Background Location';
      case PermissionType.notification:
        return 'Notifications';
      case PermissionType.camera:
        return 'Camera';
      case PermissionType.storage:
        return 'Storage';
    }
  }

  /// Map Geolocator LocationPermission to PermissionStatus
  PermissionStatus _mapLocationPermission(LocationPermission permission) {
    switch (permission) {
      case LocationPermission.denied:
        return PermissionStatus.denied;
      case LocationPermission.deniedForever:
        return PermissionStatus.permanentlyDenied;
      case LocationPermission.whileInUse:
      case LocationPermission.always:
        return PermissionStatus.granted;
      case LocationPermission.unableToDetermine:
        return PermissionStatus.denied;
    }
  }
}

/// Types of permissions used in the app
enum PermissionType {
  location,
  backgroundLocation,
  notification,
  camera,
  storage,
}

/// Status of automatic validation permissions
class AutomaticValidationPermissionStatus {
  final Map<PermissionType, PermissionStatus> permissions;
  final bool allGranted;
  final String? error;

  const AutomaticValidationPermissionStatus({
    required this.permissions,
    required this.allGranted,
    this.error,
  });

  /// Check if a specific permission is granted
  bool isPermissionGranted(PermissionType type) {
    return permissions[type] == PermissionStatus.granted;
  }

  /// Get list of denied permissions
  List<PermissionType> get deniedPermissions {
    return permissions.entries
        .where((entry) => entry.value != PermissionStatus.granted)
        .map((entry) => entry.key)
        .toList();
  }

  /// Get list of permanently denied permissions
  List<PermissionType> get permanentlyDeniedPermissions {
    return permissions.entries
        .where((entry) => entry.value == PermissionStatus.permanentlyDenied)
        .map((entry) => entry.key)
        .toList();
  }

  /// Check if any permissions are permanently denied
  bool get hasPermanentlyDeniedPermissions {
    return permanentlyDeniedPermissions.isNotEmpty;
  }

  /// Check if location permission is granted
  bool get hasLocationPermission {
    return isPermissionGranted(PermissionType.location);
  }

  /// Check if background location permission is granted
  bool get hasBackgroundLocationPermission {
    return isPermissionGranted(PermissionType.backgroundLocation);
  }

  /// Check if notification permission is granted
  bool get hasNotificationPermission {
    return isPermissionGranted(PermissionType.notification);
  }

  /// Check if all location-related permissions are granted
  bool get hasAllLocationPermissions {
    return hasLocationPermission && hasBackgroundLocationPermission;
  }

  /// Get summary message
  String get summaryMessage {
    if (error != null) {
      return 'Error checking permissions: $error';
    }
    
    if (allGranted) {
      return 'All permissions granted for automatic validation';
    }
    
    final denied = deniedPermissions;
    if (denied.isEmpty) {
      return 'All permissions granted';
    }
    
    final permissionNames = denied.map((type) => 
        PermissionService.instance.getPermissionName(type)).join(', ');
    
    return 'Missing permissions: $permissionNames';
  }
}
