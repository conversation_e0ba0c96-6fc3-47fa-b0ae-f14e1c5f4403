import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:respublicaseguridad/features/auth/domain/entities/user.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:respublicaseguridad/features/auth/presentation/bloc/auth_state.dart';
import 'package:respublicaseguridad/features/incidents/data/datasources/incident_marker_datasource.dart';
import 'package:respublicaseguridad/features/notifications/data/services/real_time_notification_service.dart';
import 'package:respublicaseguridad/features/notifications/domain/services/notification_manager.dart';
import 'package:respublicaseguridad/core/services/notification_service.dart';

/// Service manager that coordinates app-level services based on authentication state
class AppServiceManager {
  static final AppServiceManager _instance = AppServiceManager._internal();
  factory AppServiceManager() => _instance;
  AppServiceManager._internal();

  StreamSubscription<AuthState>? _authSubscription;
  Timer? _cleanupTimer;
  bool _isInitialized = false;
  bool _syncServiceStarted = false;

  /// Initialize the service manager and start listening to auth state changes
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      final authBloc = GetIt.instance<AuthBloc>();

      // Listen to auth state changes
      _authSubscription = authBloc.stream.listen(_handleAuthStateChange);

      // Also check current state in case user is already authenticated
      _handleAuthStateChange(authBloc.state);

      _isInitialized = true;
      debugPrint('✅ AppServiceManager: Initialized successfully');
    } catch (e) {
      debugPrint('❌ AppServiceManager: Failed to initialize: $e');
    }
  }

  /// Handle authentication state changes
  void _handleAuthStateChange(AuthState authState) {
    debugPrint(
      '🔄 AppServiceManager: Auth state changed to ${authState.status}',
    );

    if (authState.isAuthenticated && !_syncServiceStarted) {
      _startServices(authState.user);
    } else if (!authState.isAuthenticated && _syncServiceStarted) {
      _stopServices();
    }
  }

  /// Start all app services
  void _startServices(User user) {
    // Initialize notification services with user context
    _initializeNotificationServices(user);

    // Start cleanup timer for expired markers (runs every 6 hours)
    _startCleanupTimer();

    _syncServiceStarted = true;
    debugPrint('✅ AppServiceManager: Services started successfully');

    // Run initial cleanup
    _cleanupExpiredMarkers();
  }

  /// Initialize notification services with user context
  Future<void> _initializeNotificationServices(User user) async {
    try {
      debugPrint(
        '🔄 AppServiceManager: Initializing notification services for user: ${user.id}',
      );

      // Initialize the core notification service
      final notificationService = GetIt.instance<NotificationService>();
      await notificationService.initialize();

      // Initialize the real-time notification service
      final realTimeService = GetIt.instance<RealTimeNotificationService>();
      await realTimeService.initialize();

      // Initialize the notification manager with user context
      final notificationManager = GetIt.instance<NotificationManager>();
      final initResult = await notificationManager.initialize(user.id);

      initResult.fold(
        (failure) => debugPrint(
          '❌ AppServiceManager: Failed to initialize NotificationManager: ${failure.message}',
        ),
        (_) => debugPrint(
          '✅ AppServiceManager: NotificationManager initialized successfully',
        ),
      );

      debugPrint('✅ AppServiceManager: Notification services initialized');
    } catch (e) {
      debugPrint(
        '❌ AppServiceManager: Error initializing notification services: $e',
      );
    }
  }

  /// Start the cleanup timer for expired markers
  void _startCleanupTimer() {
    _cleanupTimer?.cancel();
    _cleanupTimer = Timer.periodic(
      const Duration(hours: 6),
      (_) => _cleanupExpiredMarkers(),
    );
    debugPrint('✅ AppServiceManager: Cleanup timer started (every 6 hours)');
  }

  /// Clean up expired markers
  Future<void> _cleanupExpiredMarkers() async {
    try {
      debugPrint('🔄 AppServiceManager: Cleaning up expired markers...');
      final markerDataSource = GetIt.instance<IncidentMarkerDataSource>();
      await markerDataSource.cleanupExpiredMarkers();
      debugPrint('✅ AppServiceManager: Expired markers cleanup completed');
    } catch (e) {
      debugPrint('❌ AppServiceManager: Error cleaning up expired markers: $e');
    }
  }

  /// Stop all app services
  void _stopServices() {
    // Cancel cleanup timer
    _cleanupTimer?.cancel();
    _cleanupTimer = null;

    // Dispose notification manager
    try {
      final notificationManager = GetIt.instance<NotificationManager>();
      notificationManager.dispose();
      debugPrint('✅ AppServiceManager: NotificationManager disposed');
    } catch (e) {
      debugPrint(
        '❌ AppServiceManager: Error disposing NotificationManager: $e',
      );
    }

    _syncServiceStarted = false;
    debugPrint('✅ AppServiceManager: Services stopped successfully');
  }

  /// Manually trigger a cleanup of expired markers
  Future<void> manualCleanup() async {
    if (!_syncServiceStarted) {
      debugPrint(
        '⚠️ AppServiceManager: Cannot perform manual cleanup - services not started',
      );
      return;
    }

    await _cleanupExpiredMarkers();
  }

  /// Dispose of resources
  void dispose() {
    _authSubscription?.cancel();
    _authSubscription = null;
    _cleanupTimer?.cancel();
    _cleanupTimer = null;
    _isInitialized = false;
    _syncServiceStarted = false;
  }
}
