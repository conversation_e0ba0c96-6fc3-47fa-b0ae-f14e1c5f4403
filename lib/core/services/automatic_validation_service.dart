import 'package:cloud_functions/cloud_functions.dart';
import 'package:respublicaseguridad/core/error/exceptions.dart';
import 'package:respublicaseguridad/core/utils/logger.dart';
import 'package:respublicaseguridad/core/services/notification_service.dart';

/// Clean and simple automatic validation service
///
/// Integrates with the industry-standard automatic zone validation
/// cloud functions that provide:
/// - Simple 3-day validation sessions
/// - Limited sampling approach (7 total samples max)
/// - Efficient resource usage with geolib
class AutomaticValidationService {
  static const String _logContext = 'AutomaticValidationService';

  final FirebaseFunctions _functions = FirebaseFunctions.instance;

  /// Triggers automatic validation for a zone
  ///
  /// Creates a simple 3-day validation session with:
  /// - 1 initial sample (1-2 minutes after activation)
  /// - 2 random samples per day during presence hours
  /// - Maximum 7 total validation attempts
  /// - Automatic expiry after 3 days
  Future<AutomaticValidationTriggerResult> triggerAutomaticValidation(
    String zoneId,
  ) async {
    const methodContext = 'triggerAutomaticValidation';

    try {
      Logger.info('$_logContext.$methodContext: Starting for zone $zoneId');

      _validateZoneId(zoneId);

      // Get current FCM token to ensure notifications work
      final notificationService = NotificationService.instance;
      final fcmToken = notificationService.fcmToken;

      final callable = _functions.httpsCallable('triggerAutomaticValidation');
      final result = await callable.call({
        'zoneId': zoneId,
        'fcmToken': fcmToken, // Pass FCM token to ensure notifications work
      });

      final data = _validateCloudFunctionResponse(result.data);

      Logger.info('$_logContext.$methodContext: Success - ${data['message']}');

      return AutomaticValidationTriggerResult.fromMap(data);
    } on FirebaseFunctionsException catch (e) {
      Logger.error(
        '$_logContext.$methodContext: Firebase Functions error - ${e.code}: ${e.message}',
      );
      throw _mapFirebaseFunctionsError(e);
    } catch (e) {
      Logger.error('$_logContext.$methodContext: Unexpected error - $e');
      throw ServerException('Failed to trigger automatic validation: $e');
    }
  }

  /// Checks if automatic validation should be active (lightweight check)
  Future<bool> shouldAutomaticValidationBeActive(String zoneId) async {
    const methodContext = 'shouldAutomaticValidationBeActive';

    try {
      _validateZoneId(zoneId);

      // Simple client-side check - let cloud function handle complex logic
      return true;
    } catch (e) {
      Logger.warning(
        '$_logContext.$methodContext: Error checking status for zone $zoneId - $e',
      );
      return false;
    }
  }

  /// Forces restart of automatic validation (for stuck sessions)
  Future<AutomaticValidationTriggerResult> forceRestartAutomaticValidation(
    String zoneId,
  ) async {
    const methodContext = 'forceRestartAutomaticValidation';

    try {
      Logger.info(
        '$_logContext.$methodContext: Force restarting validation for zone $zoneId',
      );

      _validateZoneId(zoneId);

      // Get current FCM token to ensure notifications work
      final notificationService = NotificationService.instance;
      final fcmToken = notificationService.fcmToken;

      final callable = _functions.httpsCallable('triggerAutomaticValidation');
      final result = await callable.call({
        'zoneId': zoneId,
        'fcmToken': fcmToken,
        'forceRestart': true, // Flag to indicate this is a force restart
      });

      final data = _validateCloudFunctionResponse(result.data);

      Logger.info(
        '$_logContext.$methodContext: Force restart success - ${data['message']}',
      );

      return AutomaticValidationTriggerResult.fromMap(data);
    } on FirebaseFunctionsException catch (e) {
      Logger.error(
        '$_logContext.$methodContext: Firebase Functions error - ${e.code}: ${e.message}',
      );
      throw _mapFirebaseFunctionsError(e);
    } catch (e) {
      Logger.error('$_logContext.$methodContext: Unexpected error - $e');
      throw ServerException('Failed to force restart automatic validation: $e');
    }
  }

  /// Auto-starts automatic validation if needed (simplified)
  Future<AutomaticValidationTriggerResult> autoStartValidationIfNeeded(
    String zoneId,
  ) async {
    const methodContext = 'autoStartValidationIfNeeded';

    try {
      Logger.info(
        '$_logContext.$methodContext: ===== AUTO-STARTING VALIDATION =====',
      );
      Logger.info('$_logContext.$methodContext: Zone ID: $zoneId');
      Logger.info(
        '$_logContext.$methodContext: Current time: ${DateTime.now()}',
      );

      // Check if we should first get the current status
      Logger.info(
        '$_logContext.$methodContext: Checking current validation status first...',
      );

      try {
        final currentStatus = await getValidationSessionStatus(zoneId);
        Logger.info(
          '$_logContext.$methodContext: Current status - isActive: ${currentStatus.isActive}, isStuck: ${currentStatus.isStuck}',
        );
        Logger.info(
          '$_logContext.$methodContext: Current status message: ${currentStatus.message}',
        );

        if (currentStatus.isActive && !currentStatus.isStuck) {
          Logger.info(
            '$_logContext.$methodContext: Validation already active and not stuck, returning current status',
          );
          return AutomaticValidationTriggerResult(
            success: true,
            status: ValidationStatus.monitoringStarted,
            message:
                'Validation session is already active: ${currentStatus.message}',
          );
        }
      } catch (statusError) {
        Logger.warning(
          '$_logContext.$methodContext: Could not get current status, proceeding with trigger: $statusError',
        );
      }

      // Direct trigger - cloud function handles all validation logic
      Logger.info(
        '$_logContext.$methodContext: Triggering automatic validation...',
      );
      final result = await triggerAutomaticValidation(zoneId);

      Logger.info(
        '$_logContext.$methodContext: Trigger result - success: ${result.success}, status: ${result.status}',
      );
      Logger.info(
        '$_logContext.$methodContext: Trigger message: ${result.message}',
      );

      return result;
    } catch (e, stackTrace) {
      Logger.error(
        '$_logContext.$methodContext: Error auto-starting validation for zone $zoneId - $e',
      );
      Logger.error('$_logContext.$methodContext: Stack trace: $stackTrace');

      // If it's a validation session already active, provide helpful context
      String userFriendlyMessage = _getErrorMessage(e);
      if (e is ValidationException && e.message.contains('already active')) {
        userFriendlyMessage =
            'Validation session is already running. If it appears stuck, try restarting it manually.';
        Logger.info(
          '$_logContext.$methodContext: Detected already active session',
        );
      }

      return AutomaticValidationTriggerResult(
        success: false,
        status: ValidationStatus.error,
        message: userFriendlyMessage,
        error: e.toString(),
      );
    }
  }

  /// Gets detailed validation session status
  Future<ValidationSessionStatus> getValidationSessionStatus(
    String zoneId,
  ) async {
    const methodContext = 'getValidationSessionStatus';

    try {
      _validateZoneId(zoneId);

      // Call cloud function to get detailed status
      final callable = _functions.httpsCallable('getValidationSessionStatus');
      final result = await callable.call({'zoneId': zoneId});

      final data = _validateCloudFunctionResponse(result.data);
      return ValidationSessionStatus.fromMap(data);
    } catch (e) {
      Logger.error(
        '$_logContext.$methodContext: Error getting status for zone $zoneId - $e',
      );

      return ValidationSessionStatus(
        isActive: false,
        isStuck: false,
        message: 'Unable to retrieve validation status: ${_getErrorMessage(e)}',
        error: e.toString(),
      );
    }
  }

  /// Simple location processing (no heavy computation)
  ///
  /// This is a lightweight method that acknowledges location updates
  /// without performing complex processing. The actual validation
  /// happens during scheduled samples via cloud functions.
  Future<LocationUpdateResult> processLocationUpdate({
    required String zoneId,
    required double latitude,
    required double longitude,
    required double accuracy,
  }) async {
    const methodContext = 'processLocationUpdate';

    try {
      _validateZoneId(zoneId);
      _validateCoordinates(latitude, longitude);
      _validateAccuracy(accuracy);

      Logger.info(
        '$_logContext.$methodContext: Processing update for zone $zoneId (accuracy: ${accuracy}m)',
      );

      // Simple acknowledgment - actual validation happens via cloud scheduler
      return LocationUpdateResult(
        success: true,
        isWithinZone:
            true, // Simplified - real validation happens in scheduled samples
        distanceMeters: 0.0, // Placeholder - not computed here
        message:
            'Location update acknowledged - validation occurs during scheduled samples',
      );
    } catch (e) {
      Logger.error(
        '$_logContext.$methodContext: Error processing location update - $e',
      );

      return LocationUpdateResult(
        success: false,
        isWithinZone: false,
        distanceMeters: 0.0,
        message: 'Failed to process location update: ${_getErrorMessage(e)}',
      );
    }
  }

  /// Processes pending location updates (not needed for simple approach)
  ///
  /// Returns empty list since the simplified validation approach
  /// doesn't use client-side pending update processing
  Future<List<LocationUpdateResult>> processPendingLocationUpdates(
    String zoneId,
  ) async {
    const methodContext = 'processPendingLocationUpdates';

    try {
      _validateZoneId(zoneId);

      Logger.info(
        '$_logContext.$methodContext: No pending updates needed for simple validation approach (zone: $zoneId)',
      );
      return [];
    } catch (e) {
      Logger.error('$_logContext.$methodContext: Error - $e');
      return [];
    }
  }

  /// Performs immediate validation for instant location sampling
  ///
  /// This method triggers an immediate validation check, typically used
  /// when the user taps a location request notification and grants permission.
  Future<Map<String, dynamic>> performImmediateValidation(String zoneId) async {
    const methodContext = 'performImmediateValidation';

    try {
      Logger.info(
        '$_logContext.$methodContext: Starting immediate validation for zone $zoneId',
      );

      _validateZoneId(zoneId);

      final callable = _functions.httpsCallable('performImmediateValidation');
      final result = await callable.call({'zoneId': zoneId});

      final data = _validateCloudFunctionResponse(result.data);

      Logger.info(
        '$_logContext.$methodContext: Success - ${data['message'] ?? 'Immediate validation completed'}',
      );

      return data;
    } on FirebaseFunctionsException catch (e) {
      Logger.error(
        '$_logContext.$methodContext: Firebase Functions error - ${e.code}: ${e.message}',
      );
      throw ServerException('Immediate validation failed: ${e.message}');
    } catch (e) {
      Logger.error('$_logContext.$methodContext: Unexpected error - $e');
      throw ServerException('Unexpected error during immediate validation: $e');
    }
  }

  // =====================================
  // PRIVATE HELPER METHODS
  // =====================================

  /// Validates zone ID parameter
  void _validateZoneId(String zoneId) {
    if (zoneId.isEmpty) {
      throw ValidationException('Zone ID cannot be empty');
    }

    if (zoneId.length < 10) {
      throw ValidationException('Invalid zone ID format');
    }
  }

  /// Validates GPS coordinates
  void _validateCoordinates(double latitude, double longitude) {
    if (latitude < -90 || latitude > 90) {
      throw ValidationException('Invalid latitude: $latitude');
    }

    if (longitude < -180 || longitude > 180) {
      throw ValidationException('Invalid longitude: $longitude');
    }
  }

  /// Validates GPS accuracy
  void _validateAccuracy(double accuracy) {
    if (accuracy < 0) {
      throw ValidationException('Invalid accuracy: $accuracy');
    }

    if (accuracy > 1000) {
      throw ValidationException('GPS accuracy too poor: ${accuracy}m');
    }
  }

  /// Validates cloud function response
  Map<String, dynamic> _validateCloudFunctionResponse(dynamic data) {
    if (data == null) {
      throw ServerException('Cloud function returned null response');
    }

    if (data is! Map<String, dynamic>) {
      throw ServerException('Cloud function returned invalid response format');
    }

    return data;
  }

  /// Maps Firebase Functions exceptions to domain exceptions
  Exception _mapFirebaseFunctionsError(FirebaseFunctionsException e) {
    switch (e.code) {
      case 'invalid-argument':
        return ValidationException(e.message ?? 'Invalid request parameters');
      case 'failed-precondition':
        // Enhanced error message for "already active" cases
        final message = e.message ?? 'Validation requirements not met';
        if (message.contains('already active')) {
          return ValidationException(
            'Automatic validation is currently running for this zone. '
            '${message.replaceFirst('Automatic validation already active for this zone', '')}'
            '\n\nIf the validation appears stuck, you can try to restart it.',
          );
        }
        return ValidationException(message);
      case 'permission-denied':
        return ValidationException('Permission denied - check zone ownership');
      case 'not-found':
        return ValidationException('Zone not found');
      case 'unauthenticated':
        return ValidationException('Authentication required');
      case 'resource-exhausted':
        return ValidationException(
          'Validation limit exceeded - try again later',
        );
      case 'unavailable':
        return ServerException(
          'Service temporarily unavailable - please retry',
        );
      case 'deadline-exceeded':
        return ServerException('Request timeout - please try again');
      default:
        return ServerException('Validation service error: ${e.message}');
    }
  }

  /// Gets user-friendly error message
  String _getErrorMessage(dynamic error) {
    if (error is ValidationException) {
      return error.message;
    }

    if (error is ServerException) {
      return error.message;
    }

    return 'An unexpected error occurred';
  }
}

/// Enhanced data class for location update information
class LocationUpdateData {
  final double latitude;
  final double longitude;
  final double accuracy;
  final DateTime timestamp;

  const LocationUpdateData({
    required this.latitude,
    required this.longitude,
    required this.accuracy,
    required this.timestamp,
  });

  /// Creates from Position object (geolocator package)
  factory LocationUpdateData.fromPosition(dynamic position) {
    return LocationUpdateData(
      latitude: position.latitude,
      longitude: position.longitude,
      accuracy: position.accuracy,
      timestamp: DateTime.now(),
    );
  }

  /// Converts to map for API calls
  Map<String, dynamic> toMap() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'accuracy': accuracy,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  /// Validates the location data
  bool get isValid {
    return latitude >= -90 &&
        latitude <= 90 &&
        longitude >= -180 &&
        longitude <= 180 &&
        accuracy >= 0 &&
        accuracy <= 1000;
  }

  @override
  String toString() {
    return 'LocationUpdateData(lat: $latitude, lng: $longitude, accuracy: ${accuracy}m, time: $timestamp)';
  }
}

/// Enhanced result data class for location update responses
class LocationUpdateResult {
  final bool success;
  final bool isWithinZone;
  final double distanceMeters;
  final String message;
  final String? error;

  const LocationUpdateResult({
    required this.success,
    required this.isWithinZone,
    required this.distanceMeters,
    required this.message,
    this.error,
  });

  /// Creates from Cloud Function response
  factory LocationUpdateResult.fromMap(Map<String, dynamic> map) {
    return LocationUpdateResult(
      success: map['success'] ?? false,
      isWithinZone: map['isWithinZone'] ?? false,
      distanceMeters: (map['distanceMeters'] ?? 0.0).toDouble(),
      message: map['message'] ?? '',
      error: map['error'],
    );
  }

  /// Gets distance in human-readable format
  String get distanceText {
    if (distanceMeters < 1000) {
      return '${distanceMeters.round()}m away';
    } else {
      return '${(distanceMeters / 1000).toStringAsFixed(1)}km away';
    }
  }

  /// Checks if the location update was successful
  bool get isSuccessful => success && error == null;

  @override
  String toString() {
    return 'LocationUpdateResult(success: $success, inZone: $isWithinZone, distance: $distanceText, message: $message)';
  }
}

/// Enhanced validation status enum
enum ValidationStatus {
  validationStarted('validation_started'),
  validated('validated'),
  notInZone('not_in_zone'),
  noLocation('no_location'),
  expired('expired'),
  error('error'),
  monitoringStarted('monitoring_started'),
  insufficientPresence('insufficient_presence'),
  noUpdateNeeded('no_update_needed'); // Added for immediate validation

  const ValidationStatus(this.value);
  final String value;

  static ValidationStatus fromString(String value) {
    return ValidationStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => ValidationStatus.error,
    );
  }
}

/// Enhanced result data class for automatic validation trigger responses
class AutomaticValidationTriggerResult {
  final bool success;
  final ValidationStatus status;
  final String message;
  final int? locationCount;
  final int? withinZoneCount;
  final String? error;
  final String? expiresAt;
  final int? totalSamples;

  const AutomaticValidationTriggerResult({
    required this.success,
    required this.status,
    required this.message,
    this.locationCount,
    this.withinZoneCount,
    this.error,
    this.expiresAt,
    this.totalSamples,
  });

  /// Creates from Cloud Function response
  factory AutomaticValidationTriggerResult.fromMap(Map<String, dynamic> map) {
    return AutomaticValidationTriggerResult(
      success: map['success'] ?? false,
      status: ValidationStatus.fromString(map['status'] ?? 'error'),
      message: map['message'] ?? '',
      locationCount: map['locationCount'],
      withinZoneCount: map['withinZoneCount'],
      error: map['error'],
      expiresAt: map['expiresAt'],
      totalSamples: map['totalSamples'],
    );
  }

  /// Checks if validation was completed successfully
  bool get isValidated => success && status == ValidationStatus.validated;

  /// Checks if monitoring has started
  bool get isMonitoring =>
      success && status == ValidationStatus.monitoringStarted;

  /// Checks if validation session has started
  bool get isSessionStarted =>
      success && status == ValidationStatus.validationStarted;

  /// Checks if there was insufficient presence
  bool get hasInsufficientPresence =>
      status == ValidationStatus.insufficientPresence;

  /// Gets a detailed status message for the UI
  String get detailedMessage {
    if (error != null) return 'Error: $error';

    switch (status) {
      case ValidationStatus.validated:
        return '$message (${locationCount ?? 0} location updates, ${withinZoneCount ?? 0} within zone)';
      case ValidationStatus.validationStarted:
        return '$message - Session expires: ${_formatExpiryDate()}';
      case ValidationStatus.monitoringStarted:
        return '$message - Your location will be monitored during presence hours.';
      case ValidationStatus.insufficientPresence:
        return '$message - Continue visiting the zone during your presence hours.';
      case ValidationStatus.error:
        return 'Validation failed: $message';
      default:
        return message;
    }
  }

  /// Formats expiry date for display
  String _formatExpiryDate() {
    if (expiresAt == null) return 'in 3 days';

    try {
      final expiry = DateTime.parse(expiresAt!);
      final now = DateTime.now();
      final difference = expiry.difference(now);

      if (difference.inDays > 0) {
        return 'in ${difference.inDays} days';
      } else if (difference.inHours > 0) {
        return 'in ${difference.inHours} hours';
      } else {
        return 'soon';
      }
    } catch (e) {
      return 'in 3 days';
    }
  }

  @override
  String toString() {
    return 'AutomaticValidationTriggerResult(success: $success, status: ${status.value}, message: $message)';
  }
}

/// Enhanced validation session status information
class ValidationSessionStatus {
  final bool isActive;
  final bool isStuck;
  final String message;
  final String? error;
  final int? attempts;
  final int? maxAttempts;
  final DateTime? expiresAt;
  final DateTime? lastActivity;

  const ValidationSessionStatus({
    required this.isActive,
    required this.isStuck,
    required this.message,
    this.error,
    this.attempts,
    this.maxAttempts,
    this.expiresAt,
    this.lastActivity,
  });

  /// Creates from Cloud Function response
  factory ValidationSessionStatus.fromMap(Map<String, dynamic> map) {
    return ValidationSessionStatus(
      isActive: map['isActive'] ?? false,
      isStuck: map['isStuck'] ?? false,
      message: map['message'] ?? '',
      error: map['error'],
      attempts: map['attempts'],
      maxAttempts: map['maxAttempts'],
      expiresAt:
          map['expiresAt'] != null ? DateTime.parse(map['expiresAt']) : null,
      lastActivity:
          map['lastActivity'] != null
              ? DateTime.parse(map['lastActivity'])
              : null,
    );
  }

  /// Checks if the session needs attention
  bool get needsAttention =>
      isStuck || (isActive && attempts != null && attempts! == 0);

  /// Gets time remaining for session
  String get timeRemaining {
    if (expiresAt == null) return 'Unknown';

    final now = DateTime.now();
    final difference = expiresAt!.difference(now);

    if (difference.inDays > 0) {
      return '${difference.inDays} days remaining';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hours remaining';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minutes remaining';
    } else {
      return 'Expired';
    }
  }

  @override
  String toString() {
    return 'ValidationSessionStatus(active: $isActive, stuck: $isStuck, message: $message)';
  }
}
