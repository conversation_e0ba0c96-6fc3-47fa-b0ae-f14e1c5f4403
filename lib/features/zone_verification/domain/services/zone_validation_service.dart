import 'package:dartz/dartz.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/services/automatic_validation_service.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_validation_results.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_verification_entities.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/repositories/zone_repository.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/validators/zone_business_rules.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/usecases/trigger_automatic_validation_usecase.dart' as trigger_usecase;

/// Domain service for zone validation operations
class ZoneValidationService {
  final ZoneRepository _repository;
  final AutomaticValidationService _automaticValidationService;

  const ZoneValidationService(
    this._repository, {
    required AutomaticValidationService automaticValidationService,
  }) : _automaticValidationService = automaticValidationService;

  /// Process community validation for a zone
  Future<Either<Failure, ZoneValidationResult>> processCommunityValidation({
    required String zoneId,
    required String validatorUserId,
    String? validationNote,
  }) async {
    // Get the zone
    final zoneResult = await _repository.getZoneById(zoneId);
    
    return zoneResult.fold(
      (failure) => Left(failure),
      (zone) async {
        // Check business rules
        final businessRuleCheck = ZoneBusinessRules.canUserValidateZone(zone, validatorUserId);
        if (businessRuleCheck != null) {
          return Left(ValidationFailure(businessRuleCheck));
        }

        // Add community validation
        final result = await _repository.addCommunityValidation(
          zoneId: zoneId,
          validatorUserId: validatorUserId,
        );

        return result.fold(
          (failure) => Left(failure),
          (updatedZone) async {
            // Check if zone should be auto-validated
            if (ZoneBusinessRules.shouldAutoValidateAfterCommunityValidation(updatedZone)) {
              final autoValidateResult = await _repository.updateZoneValidationStatus(
                zoneId: zoneId,
                status: ZoneStatus.validated,
              );

              return autoValidateResult.fold(
                (failure) => Left(failure),
                (finalZone) => Right(_createValidationResult(finalZone, validatorUserId, true)),
              );
            }

            return Right(_createValidationResult(updatedZone, validatorUserId, false));
          },
        );
      },
    );
  }

  /// Create validation result object
  ZoneValidationResult _createValidationResult(
    ZoneEntity zone,
    String validatorUserId,
    bool isZoneNowValidated,
  ) {
    return ZoneValidationResult(
      zone: zone,
      validationAdded: true,
      validatorUserId: validatorUserId,
      validatedAt: DateTime.now(),
      isZoneNowValidated: isZoneNowValidated,
      currentValidationCount: zone.communityValidationCount,
      requiredValidationCount: 3,
      remainingValidations: ZoneBusinessRules.getRemainingValidations(zone),
    );
  }

  /// Process automatic validation enablement
  Future<Either<Failure, AutomaticValidationResult>> processAutomaticValidationEnablement({
    required String zoneId,
    required String userId,
    required bool hasLocationPermission,
    required bool isLocationServiceEnabled,
    required bool isLocationAccurate,
  }) async {
    // Get the zone
    final zoneResult = await _repository.getZoneById(zoneId);
    
    return zoneResult.fold(
      (failure) => Left(failure),
      (zone) async {
        // Check business rules
        final businessRuleCheck = ZoneBusinessRules.canEnableAutomaticValidation(zone, userId);
        if (businessRuleCheck != null) {
          return Left(ValidationFailure(businessRuleCheck));
        }

        // Check if requirements are met
        if (!ZoneBusinessRules.areAutomaticValidationRequirementsMet(
          locationPermissionGranted: hasLocationPermission,
          locationServiceEnabled: isLocationServiceEnabled,
          locationAccuracyMet: isLocationAccurate,
        )) {
          return Right(_createAutomaticValidationResult(
            zone: zone,
            isEnabled: false,
            locationPermissionGranted: hasLocationPermission,
            locationServiceEnabled: isLocationServiceEnabled,
            locationAccuracyMet: isLocationAccurate,
            monitoringStarted: false,
          ));
        }

        // Enable automatic validation
        final result = await _repository.enableAutomaticValidation(zoneId);
        
        return result.fold(
          (failure) => Left(failure),
          (updatedZone) => Right(_createAutomaticValidationResult(
            zone: updatedZone,
            isEnabled: true,
            locationPermissionGranted: hasLocationPermission,
            locationServiceEnabled: isLocationServiceEnabled,
            locationAccuracyMet: isLocationAccurate,
            monitoringStarted: true,
          )),
        );
      },
    );
  }

  /// Trigger automatic validation using Cloud Functions
  Future<Either<Failure, AutomaticValidationResult>> triggerCloudAutomaticValidation({
    required String zoneId,
    required String userId,
  }) async {
    try {
      final result = await _automaticValidationService.triggerAutomaticValidation(zoneId);

      if (result.success == true) {
        final zoneResult = await _repository.getZoneById(zoneId);
        return zoneResult.fold(
          (failure) => Left(failure),
          (zone) => Right(AutomaticValidationResult(
            zone: zone,
            isEnabled: true,
            requiresLocationPermission: false, // Cloud Function handles this
            locationPermissionGranted: true,
            locationServiceEnabled: true,
            locationAccuracyMet: true,
            monitoringStarted: true,
            estimatedValidationTime: ZoneBusinessRules.calculateEstimatedValidationTime(zone),
            nextLocationCheck: DateTime.now().add(ZoneConstants.locationUpdateInterval),
          )),
        );
      } else {
        return Left(ValidationFailure(result.message));
      }
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  /// Process location update for automatic validation
  Future<Either<Failure, LocationUpdateResult>> processLocationUpdate({
    required String zoneId,
    required String userId,
    required double latitude,
    required double longitude,
    required double accuracy,
  }) async {
    try {
      final result = await _automaticValidationService.processLocationUpdate(
        zoneId: zoneId,
        latitude: latitude,
        longitude: longitude,
        accuracy: accuracy,
      );

      return Right(result);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  /// Create automatic validation result
  AutomaticValidationResult _createAutomaticValidationResult({
    required ZoneEntity zone,
    required bool isEnabled,
    required bool locationPermissionGranted,
    required bool locationServiceEnabled,
    required bool locationAccuracyMet,
    required bool monitoringStarted,
  }) {
    return AutomaticValidationResult(
      zone: zone,
      isEnabled: isEnabled,
      requiresLocationPermission: true,
      locationPermissionGranted: locationPermissionGranted,
      locationServiceEnabled: locationServiceEnabled,
      locationAccuracyMet: locationAccuracyMet,
      monitoringStarted: monitoringStarted,
      estimatedValidationTime: ZoneBusinessRules.calculateEstimatedValidationTime(zone),
      nextLocationCheck: DateTime.now().add(ZoneConstants.locationUpdateInterval),
    );
  }

  /// Process social validation request
  Future<Either<Failure, SocialValidationRequest>> processSocialValidationRequest({
    required String zoneId,
    required String userId,
  }) async {
    // Get the zone
    final zoneResult = await _repository.getZoneById(zoneId);
    
    return zoneResult.fold(
      (failure) => Left(failure),
      (zone) async {
        // Check business rules
        final businessRuleCheck = ZoneBusinessRules.canRequestSocialValidation(zone, userId);
        if (businessRuleCheck != null) {
          return Left(ValidationFailure(businessRuleCheck));
        }

        // Create social validation request
        final request = _createSocialValidationRequest(zone);

        // Save the request to Firestore for URL-based QR codes
        try {
          await _saveSocialValidationRequest(request);
        } catch (e) {
          return Left(ServerFailure(message: 'Failed to save social validation request: ${e.toString()}'));
        }

        return Right(request);
      },
    );
  }

  /// Create social validation request
  SocialValidationRequest _createSocialValidationRequest(ZoneEntity zone) {
    final requestId = DateTime.now().millisecondsSinceEpoch.toString();
    final validationUrl = 'https://app.respublicaseguridad.com/validate/$requestId';
    final qrCodeData = 'ZONE_VALIDATION:$requestId:${zone.id}';

    return SocialValidationRequest(
      id: requestId,
      zoneId: zone.id,
      requesterId: zone.userId,
      zoneName: zone.name,
      zoneAddress: zone.address,
      zoneType: zone.type,
      validationUrl: validationUrl,
      qrCodeData: qrCodeData,
      expiresAt: DateTime.now().add(const Duration(days: 7)),
      createdAt: DateTime.now(),
      currentValidationCount: zone.communityValidationCount,
      requiredValidationCount: 3,
    );
  }

  /// Save social validation request to Firestore
  Future<void> _saveSocialValidationRequest(SocialValidationRequest request) async {
    // This would typically be done through a repository, but for now we'll use Firestore directly
    // In a production app, you'd inject a SocialValidationRepository
    final firestore = FirebaseFirestore.instance;

    await firestore.collection('social_validation_requests').doc(request.id).set({
      'id': request.id,
      'zoneId': request.zoneId,
      'requesterId': request.requesterId,
      'zoneName': request.zoneName,
      'zoneAddress': request.zoneAddress,
      'zoneType': request.zoneType.name,
      'validationUrl': request.validationUrl,
      'qrCodeData': request.qrCodeData,
      'expiresAt': Timestamp.fromDate(request.expiresAt),
      'createdAt': Timestamp.fromDate(request.createdAt),
      'currentValidationCount': request.currentValidationCount,
      'requiredValidationCount': request.requiredValidationCount,
    });
  }
}
