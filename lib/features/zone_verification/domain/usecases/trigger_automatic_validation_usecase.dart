import 'package:dartz/dartz.dart';
import 'package:respublicaseguridad/core/error/failures.dart';
import 'package:respublicaseguridad/core/usecases/usecase.dart';
import 'package:respublicaseguridad/core/services/automatic_validation_service.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/repositories/zone_repository.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_entity.dart';
import 'package:respublicaseguridad/features/zone_verification/domain/entities/zone_enums.dart';

/// Use case for triggering automatic validation via Cloud Functions v2
/// 
/// This integrates with the clean, refactored automatic validation system
/// that uses industry-standard distance calculations and efficient Firestore operations.
class TriggerAutomaticValidationUseCase implements UseCase<AutomaticValidationResult, TriggerAutomaticValidationParams> {
  final AutomaticValidationService _automaticValidationService;
  final ZoneRepository _zoneRepository;

  TriggerAutomaticValidationUseCase({
    required AutomaticValidationService automaticValidationService,
    required ZoneRepository zoneRepository,
  }) : _automaticValidationService = automaticValidationService,
       _zoneRepository = zoneRepository;

  @override
  Future<Either<Failure, AutomaticValidationResult>> call(TriggerAutomaticValidationParams params) async {
    try {
      // 1. Get zone data to validate it exists and user owns it
      final zoneResult = await _zoneRepository.getZoneById(params.zoneId);
      
      return await zoneResult.fold(
        (failure) async => Left(failure),
        (zone) async {
          // 2. Validate zone is eligible for automatic validation
          final validationCheck = _validateZoneEligibility(zone, params.userId);
          if (validationCheck != null) {
            return Left(ValidationFailure(validationCheck));
          }

          // 3. Trigger automatic validation via Cloud Function
          try {
            final result = await _automaticValidationService.triggerAutomaticValidation(params.zoneId);
            
            return Right(AutomaticValidationResult.fromTriggerResult(
              zone: zone,
              triggerResult: result,
            ));
          } catch (e) {
            return Left(ServerFailure(message: e.toString()));
          }
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  /// Validate if zone is eligible for automatic validation
  String? _validateZoneEligibility(ZoneEntity zone, String userId) {
    // Check if user owns the zone
    if (zone.userId != userId) {
      return 'You can only trigger automatic validation for your own zones';
    }

    // Check if zone has automatic validation method
    if (zone.validationMethod != ValidationMethod.automatic) {
      return 'Zone must be configured for automatic validation';
    }

    // Check if zone has presence hours configured
    if (zone.presenceHoursConfig == null || !zone.presenceHoursConfig!.isAutomaticValidationEnabled) {
      return 'Zone must have presence hours configured for automatic validation';
    }

    // Check if zone is already validated
    if (zone.validationStatus == ZoneStatus.validated) {
      return 'Zone is already validated';
    }

    // Check if zone is rejected
    if (zone.validationStatus == ZoneStatus.rejected) {
      return 'Cannot validate a rejected zone';
    }

    return null; // Zone is eligible
  }
}

/// Parameters for triggering automatic validation
class TriggerAutomaticValidationParams {
  final String zoneId;
  final String userId;
  final bool forceValidation;

  const TriggerAutomaticValidationParams({
    required this.zoneId,
    required this.userId,
    this.forceValidation = false,
  });
}

/// Result of automatic validation trigger - Updated for Cloud Functions v2
class AutomaticValidationResult {
  final ZoneEntity zone;
  final bool isEnabled;
  final bool isActive;
  final DateTime? startedAt;
  final String message;
  final String status; // 'validated', 'insufficient_presence', 'monitoring_started', 'error'
  final int? locationCount;
  final int? withinZoneCount;
  final String? error;

  const AutomaticValidationResult({
    required this.zone,
    required this.isEnabled,
    required this.isActive,
    this.startedAt,
    required this.message,
    required this.status,
    this.locationCount,
    this.withinZoneCount,
    this.error,
  });

  /// Create from AutomaticValidationTriggerResult
  factory AutomaticValidationResult.fromTriggerResult({
    required ZoneEntity zone,
    required AutomaticValidationTriggerResult triggerResult,
  }) {
    return AutomaticValidationResult(
      zone: zone,
      isEnabled: triggerResult.success,
      isActive: triggerResult.success && (triggerResult.isMonitoring || triggerResult.isValidated),
      startedAt: triggerResult.success ? DateTime.now() : null,
      message: triggerResult.detailedMessage,
      status: triggerResult.status.value,
      locationCount: triggerResult.locationCount,
      withinZoneCount: triggerResult.withinZoneCount,
      error: triggerResult.error,
    );
  }

  /// Check if validation was successful
  bool get isSuccessful => isEnabled && error == null;

  /// Check if zone was validated
  bool get isValidated => status == 'validated';

  /// Check if monitoring has started  
  bool get isMonitoring => status == 'monitoring_started';

  /// Check if there was insufficient presence
  bool get hasInsufficientPresence => status == 'insufficient_presence';

  /// Get detailed status message for UI
  String get detailedMessage {
    if (error != null) return 'Error: $error';
    
    switch (status) {
      case 'validated':
        return 'Zone validated successfully! (${locationCount ?? 0} location updates processed)';
      case 'monitoring_started':
        return 'Monitoring started. Visit the zone during your presence hours to validate.';
      case 'insufficient_presence':
        return 'Insufficient presence detected. Continue visiting during presence hours.';
      case 'error':
        return 'Validation failed: $message';
      default:
        return message;
    }
  }

  /// Get status message for display
  String get statusMessage {
    if (error != null) return 'Error: $error';
    if (!isEnabled) return 'Automatic validation is not enabled';
    if (!isActive) return 'Automatic validation is not active';
    return message;
  }
}

/// Use case for processing location updates during automatic validation - Updated for v2
class ProcessLocationUpdateUseCase implements UseCase<LocationUpdateResult, ProcessLocationUpdateParams> {
  final AutomaticValidationService _automaticValidationService;
  final ZoneRepository _zoneRepository;

  ProcessLocationUpdateUseCase({
    required AutomaticValidationService automaticValidationService,
    required ZoneRepository zoneRepository,
  }) : _automaticValidationService = automaticValidationService,
       _zoneRepository = zoneRepository;

  @override
  Future<Either<Failure, LocationUpdateResult>> call(ProcessLocationUpdateParams params) async {
    try {
      // 1. Validate zone exists and user owns it
      final zoneResult = await _zoneRepository.getZoneById(params.zoneId);
      
      return await zoneResult.fold(
        (failure) async => Left(failure),
        (zone) async {
          // 2. Validate zone is configured for automatic validation
          if (zone.validationMethod != ValidationMethod.automatic) {
            return Left(ValidationFailure('Zone is not configured for automatic validation'));
          }

          if (zone.userId != params.userId) {
            return Left(ValidationFailure('You can only update location for your own zones'));
          }

          // 3. Process location update via Cloud Function
          try {
            final result = await _automaticValidationService.processLocationUpdate(
              zoneId: params.zoneId,
              latitude: params.latitude,
              longitude: params.longitude,
              accuracy: params.accuracy,
            );

            return Right(result);
          } catch (e) {
            return Left(ServerFailure(message: e.toString()));
          }
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }
}

/// Parameters for processing location updates - Updated for v2
class ProcessLocationUpdateParams {
  final String zoneId;
  final String userId;
  final double latitude;
  final double longitude;
  final double accuracy;

  const ProcessLocationUpdateParams({
    required this.zoneId,
    required this.userId,
    required this.latitude,
    required this.longitude,
    required this.accuracy,
  });

  /// Create from position data (geolocator package)
  factory ProcessLocationUpdateParams.fromPosition({
    required String zoneId,
    required String userId,
    required dynamic position,
  }) {
    return ProcessLocationUpdateParams(
      zoneId: zoneId,
      userId: userId,
      latitude: position.latitude,
      longitude: position.longitude,
      accuracy: position.accuracy,
    );
  }

  /// Validate parameters
  bool get isValid {
    return latitude >= -90 && 
           latitude <= 90 && 
           longitude >= -180 && 
           longitude <= 180 &&
           accuracy > 0 &&
           accuracy <= 50; // Match cloud function requirement
  }

  /// Get validation error message
  String? get validationError {
    if (latitude < -90 || latitude > 90) {
      return 'Invalid latitude: $latitude (must be between -90 and 90)';
    }
    if (longitude < -180 || longitude > 180) {
      return 'Invalid longitude: $longitude (must be between -180 and 180)';
    }
    if (accuracy <= 0) {
      return 'Invalid accuracy: $accuracy (must be positive)';
    }
    if (accuracy > 50) {
      return 'Location accuracy insufficient: ${accuracy}m (required: ≤50m)';
    }
    return null;
  }

  @override
  String toString() {
    return 'ProcessLocationUpdateParams(zoneId: $zoneId, lat: $latitude, lng: $longitude, accuracy: ${accuracy}m)';
  }
}
