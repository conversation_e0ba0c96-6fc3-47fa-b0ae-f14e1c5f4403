import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../core/theme/app_colors.dart';
import 'dart:math' as math;

class EmergencyAlertFab extends StatefulWidget {
  final VoidCallback? onPressed;
  final VoidCallback? onCall911;
  final VoidCallback? onCallFire;
  final bool isActive;
  final String? locationInfo;

  const EmergencyAlertFab({
    super.key,
    this.onPressed,
    this.onCall911,
    this.onCallFire,
    this.isActive = false,
    this.locationInfo,
  });

  @override
  State<EmergencyAlertFab> createState() => _EmergencyAlertFabState();
}

class _EmergencyAlertFabState extends State<EmergencyAlertFab>
    with TickerProviderStateMixin {
  late AnimationController _heartbeatController;
  late AnimationController _pulseController;
  late AnimationController _islandController;
  late Animation<double> _heartbeatAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _islandScaleAnimation;
  late Animation<double> _islandOpacityAnimation;

  // Swipe detection variables
  double _swipeOffset = 0.0;
  bool _isSwipeActive = false;
  String? _swipeDirection; // 'left' for 911, 'right' for fire

  @override
  void initState() {
    super.initState();

    // Heartbeat animation - continuous when active
    _heartbeatController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _heartbeatAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _heartbeatController, curve: Curves.easeInOut),
    );

    // Pulse animation - ripple effect
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _pulseController, curve: Curves.easeOut));

    // Island animation - for emergency action buttons
    _islandController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _islandScaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _islandController, curve: Curves.elasticOut),
    );

    _islandOpacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _islandController, curve: Curves.easeInOut),
    );

    // Start animations if active
    if (widget.isActive) {
      _startAnimations();
    }
  }

  @override
  void didUpdateWidget(EmergencyAlertFab oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isActive != oldWidget.isActive) {
      if (widget.isActive) {
        _startAnimations();
      } else {
        _stopAnimations();
      }
    }
  }

  void _startAnimations() {
    _heartbeatController.repeat(reverse: true);
    _pulseController.repeat();
    _islandController.forward();
  }

  void _stopAnimations() {
    _heartbeatController.stop();
    _pulseController.stop();
    _islandController.reverse();
    _heartbeatController.reset();
    _pulseController.reset();
  }

  @override
  void dispose() {
    _heartbeatController.dispose();
    _pulseController.dispose();
    _islandController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        // Emergency action island (Call 911 & Fire Department)
        if (widget.isActive)
          AnimatedBuilder(
            animation: _islandController,
            builder: (context, child) {
              return Transform.scale(
                scale: _islandScaleAnimation.value,
                child: Opacity(
                  opacity: _islandOpacityAnimation.value,
                  child: _buildEmergencyIsland(),
                ),
              );
            },
          ),

        // Subtle pulse ripple effect when active
        if (widget.isActive)
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Container(
                width: (70.w + 20.w * _pulseAnimation.value),
                height: (70.h + 20.h * _pulseAnimation.value),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: AppColors.error.withValues(
                      alpha: 0.2 * (1 - _pulseAnimation.value),
                    ),
                    width: 1.w,
                  ),
                ),
              );
            },
          ),

        // Main FAB with swipe detection and heartbeat animation
        GestureDetector(
          onPanStart: (details) {
            if (widget.isActive) {
              setState(() {
                _isSwipeActive = true;
                _swipeOffset = 0.0;
              });
            }
          },
          onPanUpdate: (details) {
            if (widget.isActive && _isSwipeActive) {
              setState(() {
                _swipeOffset = details.localPosition.dx - 35.w; // Center offset

                // Determine swipe direction
                if (_swipeOffset < -20.w) {
                  _swipeDirection = 'left'; // 911
                } else if (_swipeOffset > 20.w) {
                  _swipeDirection = 'right'; // Fire
                } else {
                  _swipeDirection = null;
                }
              });
            }
          },
          onPanEnd: (details) {
            if (widget.isActive && _isSwipeActive) {
              // Execute action based on swipe direction
              if (_swipeDirection == 'left') {
                widget.onCall911?.call();
              } else if (_swipeDirection == 'right') {
                widget.onCallFire?.call();
              }

              // Reset swipe state
              setState(() {
                _isSwipeActive = false;
                _swipeOffset = 0.0;
                _swipeDirection = null;
              });
            }
          },
          child: AnimatedBuilder(
            animation:
                widget.isActive
                    ? _heartbeatAnimation
                    : const AlwaysStoppedAnimation(1.0),
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(_swipeOffset * 0.3, 0), // Subtle follow movement
                child: Transform.scale(
                  scale:
                      widget.isActive
                          ? (1.0 + 0.05 * _heartbeatAnimation.value)
                          : 1.0,
                  child: Container(
                    width: 70.w,
                    height: 70.h,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: _getSwipeColor(),
                      border: Border.all(color: Colors.white, width: 2.w),
                      boxShadow: [
                        BoxShadow(
                          color: _getSwipeColor().withValues(alpha: 0.25),
                          blurRadius: widget.isActive ? 12.r : 8.r,
                          spreadRadius: widget.isActive ? 2.r : 1.r,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(35.r),
                        onTap: widget.onPressed,
                        child: Center(
                          child: Icon(
                            _getSwipeIcon(),
                            color: Colors.white,
                            size: 28.sp,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),

        // Clean status indicator when active
        if (widget.isActive)
          Positioned(
            top: 2.h,
            right: 2.w,
            child: Container(
              width: 16.w,
              height: 16.h,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 2.r,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Center(
                child: Container(
                  width: 8.w,
                  height: 8.h,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.error,
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildEmergencyIsland() {
    return Container(
      width: 280.w,
      height: 80.h,
      child: Row(
        children: [
          // Call 911 button (left)
          Expanded(
            child: _buildEmergencyActionButton(
              icon: FluentIcons.call_24_filled,
              label: '911',
              color: const Color(0xFF1976D2), // Police blue
              onTap: widget.onCall911,
              isCompact: true,
            ),
          ),

          SizedBox(width: 16.w),

          // Main SOS indicator
          Container(
            width: 60.w,
            height: 60.h,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: AppColors.error.withValues(alpha: 0.1),
              border: Border.all(color: AppColors.error, width: 2.w),
            ),
            child: Center(
              child: Text(
                'SOS',
                style: GoogleFonts.outfit(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w900,
                  color: AppColors.error,
                ),
              ),
            ),
          ),

          SizedBox(width: 16.w),

          // Fire Department button (right)
          Expanded(
            child: _buildEmergencyActionButton(
              icon: FluentIcons.fire_24_filled,
              label: 'Fire',
              color: const Color(0xFFD32F2F), // Fire red
              onTap: widget.onCallFire,
              isCompact: true,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmergencyActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback? onTap,
    bool isCompact = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: isCompact ? 60.h : 50.h,
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(isCompact ? 30.r : 25.r),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.3),
              offset: const Offset(0, 4),
              blurRadius: 12.r,
              spreadRadius: 0,
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: Colors.white, size: isCompact ? 18.sp : 20.sp),
            if (!isCompact) SizedBox(height: 4.h),
            Text(
              label,
              style: GoogleFonts.outfit(
                color: Colors.white,
                fontSize: isCompact ? 12.sp : 16.sp,
                fontWeight: FontWeight.w700,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper methods for swipe functionality
  Color _getSwipeColor() {
    if (_swipeDirection == 'left') {
      return const Color(0xFF1976D2); // Police blue for 911
    } else if (_swipeDirection == 'right') {
      return const Color(0xFFD32F2F); // Fire red
    }
    return AppColors.error; // Default emergency red
  }

  IconData _getSwipeIcon() {
    if (_swipeDirection == 'left') {
      return FluentIcons.call_24_filled; // Phone icon for 911
    } else if (_swipeDirection == 'right') {
      return FluentIcons.fire_24_filled; // Fire icon
    }
    return widget.isActive
        ? FluentIcons.dismiss_24_filled
        : FluentIcons.shield_error_24_filled;
  }
}
