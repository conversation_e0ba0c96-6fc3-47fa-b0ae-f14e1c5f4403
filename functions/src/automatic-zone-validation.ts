import {on<PERSON><PERSON>, HttpsError} from "firebase-functions/v2/https";
import {onSchedule} from "firebase-functions/v2/scheduler";
import {getFirestore, Timestamp, FieldValue} from "firebase-admin/firestore";
import {logger} from "firebase-functions";
import {getDistance} from "geolib";
import {FCMNotificationService, ValidationNotificationData} from "./services/fcm-notification-service";

// =====================================
// TYPES AND INTERFACES
// =====================================

interface ZoneData {
  userId: string;
  name: string;
  centerCoordinates: {
    latitude: number;
    longitude: number;
  };
  radiusInMeters: number;
  validationMethod: string;
  validationStatus: string;
  presenceHoursConfig?: PresenceHoursConfig;
  automaticValidation?: AutomaticValidationData;
}

interface AutomaticValidationData {
  isActive: boolean;
  startedAt: Timestamp;
  expiresAt: Timestamp;
  attempts: number;
  maxAttempts: number;
  lastAttempt: Timestamp | null;
  initialSampleCompleted: boolean;
  dailyAttempts: Record<string, number>;
}

interface PresenceHoursConfig {
  isAutomaticValidationEnabled: boolean;
  timeBlocks: TimeBlock[];
}

interface TimeBlock {
  isEnabled: boolean;
  activeDays: string[];
  startTime: string;
  endTime: string;
}

interface UserLocation {
  latitude: number;
  longitude: number;
  timestamp: Timestamp;
  accuracy?: number;
}

interface ValidationResult {
  success: boolean;
  status: 'validation_started' | 'validated' | 'not_in_zone' | 'no_location' | 'expired';
  message: string;
  expiresAt?: string;
  totalSamples?: number;
  distance?: number;
}

interface ValidationSessionStatusResult {
  isActive: boolean;
  isStuck: boolean;
  message: string;
  attempts?: number;
  maxAttempts?: number;
  expiresAt?: string;
  lastActivity?: string;
  hoursRemaining?: number;
  minutesRemaining?: number;
}

type SampleType = 'initial' | 'daily';

// =====================================
// CONSTANTS
// =====================================

const VALIDATION_CONFIG = {
  TOTAL_DAYS: 3,
  SAMPLES_PER_DAY: 2,
  MAX_TOTAL_ATTEMPTS: 7,
  INITIAL_DELAY_MINUTES: [1, 2] as const,
  LOCATION_FRESHNESS_MINUTES: 10,
  SCHEDULER_INTERVAL_MINUTES: 5,
  RANDOM_SAMPLE_PROBABILITY: 0.1,
} as const;

const VALIDATION_ERRORS = {
  INVALID_ZONE_ID: "Valid zone ID is required",
  UNAUTHENTICATED: "Authentication required",
  ZONE_NOT_FOUND: "Zone not found",
  ACCESS_DENIED: "Access denied",
  ALREADY_ACTIVE: "Automatic validation already active for this zone",
  INTERNAL_ERROR: "Internal server error",
} as const;

// =====================================
// MAIN CLOUD FUNCTIONS
// =====================================

/**
 * Triggers automatic validation for a zone
 * 
 * Creates a simple 3-day validation session that:
 * - Performs 1 initial sample within 1-2 minutes
 * - Performs 2 random samples per day during presence hours
 * - Expires after 3 days or 7 total attempts
 * - Ensures user has notification preferences for alerts
 * 
 * @param request - Cloud Functions request with zoneId and optional fcmToken
 * @returns ValidationResult with session details
 */
export const triggerAutomaticValidation = onCall({
  region: "us-central1",
}, async (request): Promise<ValidationResult> => {
  const context = "triggerAutomaticValidation";
  
  try {
    // Input validation
    const {zoneId, fcmToken, forceRestart} = validateTriggerRequest(request);
    
    // Authentication check
    if (!request.auth) {
      throw new HttpsError("unauthenticated", VALIDATION_ERRORS.UNAUTHENTICATED);
    }
    
    const firestore = getFirestore();
    const zoneData = await getZoneWithValidation(firestore, zoneId, request.auth.uid);
    
    // Check if validation is already active (with proper expiry and stuck session handling)
    if (zoneData.automaticValidation?.isActive) {
      const validation = zoneData.automaticValidation;
    const now = new Date();
    
      // If force restart is requested, clean up existing session
      if (forceRestart) {
        logger.info(`${context}: Force restart requested, cleaning up existing session`, {
          zoneId,
          currentAttempts: validation.attempts,
          sessionAge: now.getTime() - validation.startedAt.toDate().getTime()
        });
        
        await firestore.collection("zones").doc(zoneId).update({
          'automaticValidation.isActive': false,
          'automaticValidation.forceRestartedAt': Timestamp.fromDate(now),
          updatedAt: Timestamp.fromDate(now),
        });
        
        // Continue with creating new session
      } else if (validation.expiresAt.toDate() <= now) {
        logger.info(`${context}: Cleaning up expired validation session`, {
          zoneId,
          expiredAt: validation.expiresAt.toDate().toISOString(),
          now: now.toISOString()
        });
        
        // Clean up expired session
        await firestore.collection("zones").doc(zoneId).update({
          'automaticValidation.isActive': false,
          'automaticValidation.expiredAt': Timestamp.fromDate(now),
          updatedAt: Timestamp.fromDate(now),
        });
        
        // Continue with creating new session since old one expired
      } else if (validation.attempts >= validation.maxAttempts) {
        logger.info(`${context}: Cleaning up validation session with max attempts reached`, {
          zoneId,
          attempts: validation.attempts,
          maxAttempts: validation.maxAttempts
        });
        
        // Clean up session that reached max attempts
        await firestore.collection("zones").doc(zoneId).update({
          'automaticValidation.isActive': false,
          'automaticValidation.completedAt': Timestamp.fromDate(now),
          updatedAt: Timestamp.fromDate(now),
        });
        
        // Continue with creating new session
        } else {
        // Check if session appears to be stuck (no progress in last 2 hours)
        const lastAttemptDate = validation.lastAttempt?.toDate();
        const sessionStartDate = validation.startedAt.toDate();
        const timeSinceLastActivity = lastAttemptDate || sessionStartDate;
        const hoursSinceActivity = (now.getTime() - timeSinceLastActivity.getTime()) / (1000 * 60 * 60);
        
        if (hoursSinceActivity > 2 && validation.attempts === 0) {
          logger.warn(`${context}: Validation session appears stuck, allowing restart`, {
            zoneId,
            hoursSinceActivity,
            attempts: validation.attempts,
            lastAttempt: lastAttemptDate?.toISOString(),
            startedAt: sessionStartDate.toISOString()
          });
          
          // Mark old session as stuck and clean it up
          await firestore.collection("zones").doc(zoneId).update({
            'automaticValidation.isActive': false,
            'automaticValidation.stuckAt': Timestamp.fromDate(now),
            updatedAt: Timestamp.fromDate(now),
          });
          
          // Continue with creating new session
        } else {
          // Session is truly active and should not be restarted
          const hoursRemaining = Math.max(0, (validation.expiresAt.toDate().getTime() - now.getTime()) / (1000 * 60 * 60));
          const attemptsRemaining = Math.max(0, validation.maxAttempts - validation.attempts);
          
          throw new HttpsError("failed-precondition", 
            `Automatic validation is already active for this zone. ` +
            `Session expires in ${Math.ceil(hoursRemaining)} hours with ${attemptsRemaining} attempts remaining. ` +
            `Current attempts: ${validation.attempts}/${validation.maxAttempts}. ` +
            `Last activity: ${(lastAttemptDate || sessionStartDate).toISOString()}`
          );
        }
      }
    }

    // Ensure user has notification preferences set up for validation alerts
    await ensureNotificationPreferences(firestore, request.auth.uid, fcmToken);

    // Create validation session
    const now = new Date();
    const expiresAt = new Date(now.getTime() + (VALIDATION_CONFIG.TOTAL_DAYS * 24 * 60 * 60 * 1000));
    
    const automaticValidation: AutomaticValidationData = {
      isActive: true,
      startedAt: Timestamp.fromDate(now),
      expiresAt: Timestamp.fromDate(expiresAt),
      attempts: 0,
      maxAttempts: VALIDATION_CONFIG.MAX_TOTAL_ATTEMPTS,
      lastAttempt: null,
      initialSampleCompleted: false,
      dailyAttempts: {},
    };

    // Update zone document
    await firestore.collection("zones").doc(zoneId).update({
      validationMethod: 'automatic',
      automaticValidation,
      updatedAt: Timestamp.fromDate(now),
    });

    const initialDelayMinutes = calculateInitialDelay();
    
    logger.info(`${context}: Validation started for zone ${zoneId}`, {
      zoneId,
      userId: zoneData.userId,
      expiresAt: expiresAt.toISOString(),
      initialDelayMinutes,
      hasNotificationPreferences: !!fcmToken,
    });

    return {
      success: true,
      status: 'validation_started',
      message: `Automatic validation started! Initial sample in ${Math.round(initialDelayMinutes)} minutes, then 2 random samples per day for 3 days.`,
      expiresAt: expiresAt.toISOString(),
      totalSamples: VALIDATION_CONFIG.MAX_TOTAL_ATTEMPTS,
    };

  } catch (error) {
    logger.error(`${context}: Error`, {error, zoneId: request.data?.zoneId});
    
    if (error instanceof HttpsError) {
      throw error;
    }
    
    throw new HttpsError("internal", VALIDATION_ERRORS.INTERNAL_ERROR);
  }
});

/**
 * Performs immediate validation attempt for a zone
 * 
 * This function bypasses the scheduler and attempts validation immediately.
 * Useful for testing and ensuring at least one validation attempt happens quickly.
 */
export const performImmediateValidation = onCall({
  region: "us-central1",
}, async (request): Promise<ValidationResult> => {
  const context = "performImmediateValidation";
    
  try {
    // Input validation
    const {zoneId} = validateTriggerRequest(request);

    // Authentication check
    if (!request.auth) {
      throw new HttpsError("unauthenticated", VALIDATION_ERRORS.UNAUTHENTICATED);
    }

    const firestore = getFirestore();
    const zoneData = await getZoneWithValidation(firestore, zoneId, request.auth.uid);
    
    // Check if validation session is active
    if (!zoneData.automaticValidation?.isActive) {
      throw new HttpsError("failed-precondition", "No active validation session found. Start validation first.");
    }

    const now = new Date();
    const validation = zoneData.automaticValidation;

    // Check if session is expired or max attempts reached
    if (validation.expiresAt.toDate() <= now) {
      throw new HttpsError("failed-precondition", "Validation session has expired");
    }

    if (validation.attempts >= validation.maxAttempts) {
      throw new HttpsError("failed-precondition", "Maximum validation attempts reached");
    }

    logger.info(`${context}: Attempting immediate validation`, {
      zoneId,
      userId: zoneData.userId,
      currentAttempts: validation.attempts,
      maxAttempts: validation.maxAttempts
    });

    // Get user location
    let userLocation = await getUserLocation(firestore, zoneData.userId, now);
    
    if (!userLocation) {
      // If no recent location, try to be more lenient for immediate validation
      const userLocationDoc = await firestore
        .collection("user_locations")
        .doc(zoneData.userId)
        .get();

      if (!userLocationDoc.exists) {
        return {
          success: false,
          status: 'no_location',
          message: 'No location data found. Please ensure location services are enabled and try again.',
        };
      }

      const locationData = userLocationDoc.data() as UserLocation;
      if (!locationData?.latitude || !locationData?.longitude) {
      return {
          success: false,
          status: 'no_location',
          message: 'Invalid location data found. Please update your location and try again.',
        };
      }

      // Use the location data even if it's not super fresh for immediate validation
      const locationTime = locationData.timestamp?.toDate();
      const locationAgeMinutes = locationTime ? (now.getTime() - locationTime.getTime()) / (60 * 1000) : 999;
      
      logger.warn(`${context}: Using potentially stale location data`, {
        zoneId,
        locationAgeMinutes: Math.round(locationAgeMinutes)
      });
      
      // Use the location data anyway
      userLocation = locationData;
    }

    // Calculate distance and validate
    const distance = getDistance(
      { latitude: userLocation.latitude, longitude: userLocation.longitude },
      { latitude: zoneData.centerCoordinates.latitude, longitude: zoneData.centerCoordinates.longitude }
    );

    const isWithinZone = distance <= zoneData.radiusInMeters;
    const result = isWithinZone ? 'validated' : 'not_in_zone';

    // Determine sample type for immediate validation
    const sampleType: SampleType = validation.initialSampleCompleted ? 'daily' : 'initial';
    const currentDay = Math.floor((now.getTime() - validation.startedAt.toDate().getTime()) / (24 * 60 * 60 * 1000)) + 1;

    // Update zone with results
    await updateZoneWithSampleResult(firestore, zoneId, sampleType, currentDay, result, now, distance, zoneData.radiusInMeters);

    // Send notification to user about validation result
    await sendValidationResultNotification(
      firestore,
      zoneData.userId,
      zoneId,
      zoneData.name,
      result as 'validated' | 'not_in_zone',
      sampleType,
      distance,
      zoneData.radiusInMeters,
      validation.attempts,
      validation.maxAttempts
    );

    logger.info(`${context}: Immediate validation completed`, {
      zoneId,
      result,
      distance,
      radius: zoneData.radiusInMeters,
      isWithinZone,
      sampleType,
      newAttemptCount: validation.attempts + 1
    });

      return {
        success: true,
      status: result,
      message: result === 'validated' 
        ? `✅ Zone validated! You are ${Math.round(distance)}m from center (within ${zoneData.radiusInMeters}m radius).`
        : `❌ Validation failed. You are ${Math.round(distance)}m from zone center (need to be within ${zoneData.radiusInMeters}m).`,
      distance,
      totalSamples: validation.maxAttempts,
    };

  } catch (error) {
    logger.error(`${context}: Error`, {error, zoneId: request.data?.zoneId});
    
    if (error instanceof HttpsError) {
      throw error;
    }
    
    throw new HttpsError("internal", VALIDATION_ERRORS.INTERNAL_ERROR);
  }
});

/**
 * Processes automatic validation samples on schedule
 * 
 * Runs every 5 minutes to check for zones needing validation samples:
 * - Initial samples (1-2 minutes after activation)
 * - Daily samples (random times during presence hours)
 * - Cleanup expired sessions
 */
export const processSimpleAutomaticValidation = onSchedule({
  schedule: `*/${VALIDATION_CONFIG.SCHEDULER_INTERVAL_MINUTES} * * * *`,
  timeZone: "America/Mexico_City",
  region: "us-central1",
}, async () => {
  const context = "processSimpleAutomaticValidation";
  
  try {
    const firestore = getFirestore();
    const now = new Date();
    
    logger.info(`${context}: Starting validation processing at ${now.toISOString()}`);

    const activeZones = await getActiveValidationZones(firestore, now);
    
    if (activeZones.length === 0) {
      logger.info(`${context}: No active validations found`);
      return;
    }

    logger.info(`${context}: Processing ${activeZones.length} zones`, {
      zoneIds: activeZones.map(z => z.id),
      currentTime: now.toISOString()
    });

    const results = await Promise.allSettled(
      activeZones.map(({id, data}) => 
        processZoneValidationSample(firestore, id, data, now)
      )
    );

    const successCount = results.filter(r => r.status === 'fulfilled').length;
    const failureCount = results.filter(r => r.status === 'rejected').length;

    // Log failed results for debugging
    results.forEach((result, index) => {
      if (result.status === 'rejected') {
        logger.error(`${context}: Zone ${activeZones[index].id} processing failed`, {
          zoneId: activeZones[index].id,
          error: result.reason
        });
      }
    });

    logger.info(`${context}: Completed processing`, {
      total: activeZones.length,
      successful: successCount,
      failed: failureCount,
      timestamp: now.toISOString()
    });

  } catch (error) {
    logger.error(`${context}: Processing error`, {error});
  }
});

/**
 * Gets detailed validation session status for a zone
 */
export const getValidationSessionStatus = onCall({
  region: "us-central1",
}, async (request): Promise<ValidationSessionStatusResult> => {
  const context = "getValidationSessionStatus";
    
  try {
    // Input validation
    const {zoneId} = validateTriggerRequest(request);

    // Authentication check
    if (!request.auth) {
      throw new HttpsError("unauthenticated", VALIDATION_ERRORS.UNAUTHENTICATED);
    }

    const firestore = getFirestore();
    const zoneData = await getZoneWithValidation(firestore, zoneId, request.auth.uid);
    
    if (!zoneData.automaticValidation?.isActive) {
      return {
        isActive: false,
        isStuck: false,
        message: 'No active validation session found',
        attempts: 0,
        maxAttempts: VALIDATION_CONFIG.MAX_TOTAL_ATTEMPTS,
      };
    }

    const validation = zoneData.automaticValidation;
    const now = new Date();
    
    // Check if session is expired
    if (validation.expiresAt.toDate() <= now) {
      return {
        isActive: false,
        isStuck: false,
        message: 'Validation session has expired',
        attempts: validation.attempts,
        maxAttempts: validation.maxAttempts,
        expiresAt: validation.expiresAt.toDate().toISOString(),
      };
    }

    // Check if session appears stuck
    const lastAttemptDate = validation.lastAttempt?.toDate();
    const sessionStartDate = validation.startedAt.toDate();
    const timeSinceLastActivity = lastAttemptDate || sessionStartDate;
    const hoursSinceActivity = (now.getTime() - timeSinceLastActivity.getTime()) / (1000 * 60 * 60);
    
    const isStuck = hoursSinceActivity > 2 && validation.attempts === 0;
    
    // Calculate time remaining
    const timeRemaining = Math.max(0, validation.expiresAt.toDate().getTime() - now.getTime());
    const hoursRemaining = Math.floor(timeRemaining / (1000 * 60 * 60));
    const minutesRemaining = Math.floor((timeRemaining % (1000 * 60 * 60)) / (1000 * 60));

    let statusMessage = '';
    if (isStuck) {
      statusMessage = 'Session appears to be stuck. No progress detected.';
    } else if (validation.attempts === 0) {
      statusMessage = 'Waiting for initial validation attempt...';
    } else if (validation.attempts < validation.maxAttempts) {
      statusMessage = `Validation in progress. ${validation.maxAttempts - validation.attempts} attempts remaining.`;
    } else {
      statusMessage = 'Maximum attempts reached. Session will complete soon.';
    }

    logger.info(`${context}: Status retrieved for zone ${zoneId}`, {
      attempts: validation.attempts,
      maxAttempts: validation.maxAttempts,
      isStuck,
      hoursRemaining,
    });

    return {
      isActive: true,
      isStuck,
      message: statusMessage,
      attempts: validation.attempts,
      maxAttempts: validation.maxAttempts,
      expiresAt: validation.expiresAt.toDate().toISOString(),
      lastActivity: timeSinceLastActivity.toISOString(),
      hoursRemaining,
      minutesRemaining,
    };

  } catch (error) {
    logger.error(`${context}: Error getting status`, {error, zoneId: request.data?.zoneId});
    throw new HttpsError("internal", VALIDATION_ERRORS.INTERNAL_ERROR);
  }
});

// =====================================
// CORE VALIDATION LOGIC
// =====================================

/**
 * Processes a validation sample for a single zone
 */
async function processZoneValidationSample(
  firestore: FirebaseFirestore.Firestore,
  zoneId: string,
  zoneData: ZoneData,
  now: Date
): Promise<void> {
  const context = "processZoneValidationSample";
  const validation = zoneData.automaticValidation;

  logger.info(`${context}: Processing zone ${zoneId}`, {
    zoneId,
    isActive: validation?.isActive,
    attempts: validation?.attempts,
    maxAttempts: validation?.maxAttempts,
    startedAt: validation?.startedAt?.toDate()?.toISOString(),
    expiresAt: validation?.expiresAt?.toDate()?.toISOString(),
    initialSampleCompleted: validation?.initialSampleCompleted
  });

  if (!validation?.isActive) {
    logger.warn(`${context}: Zone ${zoneId} validation not active`);
    return;
  }

  try {
    // Check expiration and limits
    if (await handleValidationExpiry(firestore, zoneId, validation, now)) {
      logger.info(`${context}: Zone ${zoneId} validation expired or reached max attempts`);
      return;
    }

    // Determine sample type and eligibility
    const sampleInfo = determineSampleType(validation, now);
    
    if (!sampleInfo) {
      logger.info(`${context}: No sample needed for zone ${zoneId} at this time`, {
        daysSinceStart: Math.floor((now.getTime() - validation.startedAt.toDate().getTime()) / (24 * 60 * 60 * 1000)),
        minutesSinceStart: Math.floor((now.getTime() - validation.startedAt.toDate().getTime()) / (60 * 1000)),
        initialSampleCompleted: validation.initialSampleCompleted,
        dailyAttempts: validation.dailyAttempts
      });
      return; // No sample needed at this time
    }

    logger.info(`${context}: Sample needed for zone ${zoneId}`, {
      sampleType: sampleInfo.type,
      day: sampleInfo.day
    });

    // Check presence hours for daily samples
    if (sampleInfo.type === 'daily') {
      const isInPresenceHours = isWithinPresenceHours(zoneData.presenceHoursConfig, now);
      
      logger.info(`${context}: Daily sample check for zone ${zoneId}`, {
        isInPresenceHours,
        hasPresenceConfig: !!zoneData.presenceHoursConfig,
        isAutomaticValidationEnabled: zoneData.presenceHoursConfig?.isAutomaticValidationEnabled
      });
      
      // If presence hours are configured, respect them
      if (zoneData.presenceHoursConfig?.isAutomaticValidationEnabled && !isInPresenceHours) {
        logger.info(`${context}: Zone ${zoneId} not in presence hours, skipping sample`);
        return;
      }

      // Increased probability from 10% to 30% for better validation chances
      // If no presence hours configured, allow validation at any time
      const probabilityThreshold = zoneData.presenceHoursConfig?.isAutomaticValidationEnabled 
        ? VALIDATION_CONFIG.RANDOM_SAMPLE_PROBABILITY 
        : 0.3; // Higher chance when no presence hours configured
        
      const randomValue = Math.random();
      logger.info(`${context}: Probability check for zone ${zoneId}`, {
        randomValue,
        probabilityThreshold,
        willProceed: randomValue < probabilityThreshold
      });
        
      if (randomValue >= probabilityThreshold) {
        logger.info(`${context}: Zone ${zoneId} failed probability check, skipping sample`);
        return;
      }
    }

    logger.info(`${context}: Performing validation sample for zone ${zoneId}`, {
      sampleType: sampleInfo.type,
      day: sampleInfo.day
    });

    // Perform the validation sample
    await performValidationSample(firestore, zoneId, zoneData, sampleInfo.type, sampleInfo.day);

  } catch (error) {
    logger.error(`${context}: Error processing zone ${zoneId}`, {error, zoneId});
  }
}

/**
 * Performs the actual validation sample
 */
async function performValidationSample(
  firestore: FirebaseFirestore.Firestore,
  zoneId: string,
  zoneData: ZoneData,
  type: SampleType,
  day?: number
): Promise<void> {
  const context = "performValidationSample";
  const now = new Date();

  try {
    logger.info(`${context}: Starting ${type} sample`, {
      zoneId,
      type,
      day,
      userId: zoneData.userId,
    });

    // Get user location
    const userLocation = await getUserLocation(firestore, zoneData.userId, now);
    
    if (!userLocation) {
      logger.warn(`${context}: No valid location data`, {userId: zoneData.userId, zoneId});
      
      // Send notification to prompt user for location access
      await sendLocationRequestNotification(
        firestore,
        zoneData.userId,
        zoneId,
        zoneData.name,
        type,
        zoneData.automaticValidation?.attempts || 0,
        zoneData.automaticValidation?.maxAttempts || VALIDATION_CONFIG.MAX_TOTAL_ATTEMPTS
      );
      
      return;
    }

    // Calculate distance and validate
    const distance = getDistance(
      { latitude: userLocation.latitude, longitude: userLocation.longitude },
      { latitude: zoneData.centerCoordinates.latitude, longitude: zoneData.centerCoordinates.longitude }
    );

    const isWithinZone = distance <= zoneData.radiusInMeters;
    const result = isWithinZone ? 'validated' : 'not_in_zone';

    // Update zone with results
    await updateZoneWithSampleResult(firestore, zoneId, type, day, result, now, distance, zoneData.radiusInMeters);

    // Send notification to user about validation result
    await sendValidationResultNotification(
      firestore,
      zoneData.userId,
      zoneId,
      zoneData.name,
      result as 'validated' | 'not_in_zone',
      type,
      distance,
      zoneData.radiusInMeters,
      zoneData.automaticValidation?.attempts || 0,
      zoneData.automaticValidation?.maxAttempts || VALIDATION_CONFIG.MAX_TOTAL_ATTEMPTS
    );

    logger.info(`${context}: Sample completed`, {
      zoneId,
      type,
      result,
      distance,
      radius: zoneData.radiusInMeters,
      isWithinZone,
    });

  } catch (error) {
    logger.error(`${context}: Sample error`, {error, zoneId, type, day});
  }
}

/**
 * Sends validation result notification to user
 */
async function sendValidationResultNotification(
  firestore: FirebaseFirestore.Firestore,
  userId: string,
  zoneId: string,
  zoneName: string,
  validationResult: 'validated' | 'not_in_zone',
  sampleType: SampleType,
  distance: number,
  radiusInMeters: number,
  currentAttempt: number,
  totalAttempts: number
): Promise<void> {
  const context = "sendValidationResultNotification";
  
    try {
    // Get user's FCM token from notification preferences (same as incident notifications)
    const userPrefsDoc = await firestore.collection("notification_preferences").doc(userId).get();
    
    if (!userPrefsDoc.exists) {
      logger.warn(`${context}: User notification preferences not found`, {userId, zoneId});
      return;
    }

    const userPrefs = userPrefsDoc.data();
    const fcmToken = userPrefs?.fcmToken;

    if (!fcmToken) {
      logger.warn(`${context}: No FCM token found in notification preferences`, {userId, zoneId});
      return;
    }

    // Prepare notification data
    const validationNotificationData: ValidationNotificationData = {
      zoneId,
      zoneName,
      validationResult,
      sampleType,
      distance,
      radiusInMeters,
      timestamp: new Date().toISOString(),
      attempt: currentAttempt + 1, // Increment because we're about to update
      totalAttempts,
    };

    // Send notification
    const result = await FCMNotificationService.sendValidationResultNotification(
      userId,
      fcmToken,
      validationNotificationData
    );

    if (result.success) {
      logger.info(`${context}: Notification sent successfully`, {
        userId,
        zoneId,
        validationResult,
        sampleType,
      });
    } else {
             logger.warn(`${context}: Notification failed`, {
        userId,
        zoneId,
        error: result.error,
      });
    }

  } catch (error) {
    logger.error(`${context}: Error sending notification`, {
      error,
      userId,
      zoneId,
      validationResult,
    });
  }
}

/**
 * Ensures user has notification preferences set up for validation alerts
 */
async function ensureNotificationPreferences(
  firestore: FirebaseFirestore.Firestore,
  userId: string,
  fcmToken?: string
): Promise<void> {
  const context = "ensureNotificationPreferences";
  
  try {
    // Check if user already has notification preferences
    const prefsDoc = await firestore.collection("notification_preferences").doc(userId).get();
    
    if (prefsDoc.exists) {
      // User has preferences - update FCM token if provided and different
      const existingPrefs = prefsDoc.data();
      if (fcmToken && existingPrefs?.fcmToken !== fcmToken) {
        await firestore.collection("notification_preferences").doc(userId).update({
          fcmToken,
          fcmTokenUpdatedAt: Timestamp.fromDate(new Date()),
        });
        logger.info(`${context}: Updated FCM token for existing preferences`, {userId});
      }
    } else {
      // Create basic notification preferences for validation alerts
      const basicPreferences = {
        userId,
        isLocationNotificationsEnabled: true,
        isLocationTrackingConsented: false, // User can enable this separately
        locationConsentGrantedAt: null,
        locationAccessDurationHours: 24,
        notificationRadiusKm: 5,
        enabledIncidentTypes: ['validation'], // At minimum, enable validation notifications
        priorityLevel: 'medium',
        fcmToken: fcmToken || null,
        fcmTokenUpdatedAt: fcmToken ? Timestamp.fromDate(new Date()) : null,
        createdAt: Timestamp.fromDate(new Date()),
        createdBy: 'automatic_validation',
      };
      
      await firestore.collection("notification_preferences").doc(userId).set(basicPreferences);
      
      logger.info(`${context}: Created basic notification preferences for validation`, {
        userId,
        hasFcmToken: !!fcmToken,
      });
    }
  } catch (error) {
    logger.error(`${context}: Error ensuring notification preferences`, {error, userId});
    // Don't throw error - validation can continue without notifications
  }
}

/**
 * Sends a notification to prompt the user to enable location services.
 */
async function sendLocationRequestNotification(
  firestore: FirebaseFirestore.Firestore,
  userId: string, 
  zoneId: string, 
  zoneName: string,
  sampleType: SampleType,
  currentAttempt: number,
  totalAttempts: number
): Promise<void> {
  const context = "sendLocationRequestNotification";
  
  try {
    // Get user's FCM token from notification preferences (same as incident notifications)
    const userPrefsDoc = await firestore.collection("notification_preferences").doc(userId).get();
    
    if (!userPrefsDoc.exists) {
      logger.warn(`${context}: User notification preferences not found for location request`, {userId, zoneId});
      return;
    }

    const userPrefs = userPrefsDoc.data();
    const fcmToken = userPrefs?.fcmToken;

    if (!fcmToken) {
      logger.warn(`${context}: No FCM token found in notification preferences for location request`, {userId, zoneId});
      return;
    }

    // Prepare notification data
    const locationRequestNotificationData: ValidationNotificationData = {
      zoneId,
      zoneName,
      validationResult: 'no_location', // Indicate it's a location request
      sampleType,
      distance: 0, // No distance for location request
      radiusInMeters: 0, // No radius for location request
      timestamp: new Date().toISOString(),
      attempt: currentAttempt + 1, // Increment because we're about to update
      totalAttempts,
    };

    // Send notification
    const result = await FCMNotificationService.sendValidationResultNotification(
      userId,
      fcmToken,
      locationRequestNotificationData
    );

    if (result.success) {
      logger.info(`${context}: Location request notification sent successfully`, {
          userId,
        zoneId,
        sampleType,
      });
      } else {
      logger.warn(`${context}: Location request notification failed`, {
        userId,
        zoneId,
        error: result.error,
      });
    }

  } catch (error) {
    logger.error(`${context}: Error sending location request notification`, {
      error,
      userId,
      zoneId,
      sampleType,
    });
  }
}

// =====================================
// HELPER FUNCTIONS
// =====================================

/**
 * Validates the trigger request parameters
 */
function validateTriggerRequest(request: any): {zoneId: string; fcmToken?: string; forceRestart?: boolean} {
  const {zoneId, fcmToken, forceRestart} = request.data || {};
  
  if (!zoneId || typeof zoneId !== 'string') {
    throw new HttpsError("invalid-argument", VALIDATION_ERRORS.INVALID_ZONE_ID);
  }
  
  return {zoneId, fcmToken, forceRestart: forceRestart === true};
}

/**
 * Gets zone data with validation
 */
async function getZoneWithValidation(
  firestore: FirebaseFirestore.Firestore,
  zoneId: string,
  userId: string
): Promise<ZoneData> {
  const zoneDoc = await firestore.collection("zones").doc(zoneId).get();
  
  if (!zoneDoc.exists) {
    throw new HttpsError("not-found", VALIDATION_ERRORS.ZONE_NOT_FOUND);
  }

  const zoneData = zoneDoc.data() as ZoneData;
  
  if (zoneData.userId !== userId) {
    throw new HttpsError("permission-denied", VALIDATION_ERRORS.ACCESS_DENIED);
  }

  return zoneData;
}

/**
 * Gets zones with active automatic validation
 */
async function getActiveValidationZones(
  firestore: FirebaseFirestore.Firestore,
  now: Date
): Promise<Array<{id: string; data: ZoneData}>> {
  const activeValidationQuery = await firestore
    .collection("zones")
    .where("automaticValidation.isActive", "==", true)
    .where("automaticValidation.expiresAt", ">", Timestamp.fromDate(now))
    .limit(100)
    .get();

  return activeValidationQuery.docs.map(doc => ({
    id: doc.id,
    data: doc.data() as ZoneData,
  }));
}

/**
 * Handles validation expiry and limits
 */
async function handleValidationExpiry(
  firestore: FirebaseFirestore.Firestore,
  zoneId: string,
  validation: AutomaticValidationData,
  now: Date
): Promise<boolean> {
  // Check if expired
  if (validation.expiresAt.toDate() <= now) {
    await firestore.collection("zones").doc(zoneId).update({
      'automaticValidation.isActive': false,
    });
    logger.info("Validation expired", {zoneId});
    return true;
  }

  // Check if max attempts reached
  if (validation.attempts >= validation.maxAttempts) {
    await firestore.collection("zones").doc(zoneId).update({
      'automaticValidation.isActive': false,
    });
    logger.info("Max attempts reached", {zoneId, attempts: validation.attempts});
    return true;
  }

  return false;
}

/**
 * Determines what type of sample should be performed
 */
function determineSampleType(
  validation: AutomaticValidationData,
  now: Date
): {type: SampleType; day?: number} | null {
  const daysSinceStart = Math.floor(
    (now.getTime() - validation.startedAt.toDate().getTime()) / (24 * 60 * 60 * 1000)
  );
  
  // Initial sample - extended window to 2 hours for better reliability
  if (!validation.initialSampleCompleted && daysSinceStart === 0) {
    const minutesSinceStart = (now.getTime() - validation.startedAt.toDate().getTime()) / (60 * 1000);
    
    // Extended from 10 minutes to 120 minutes (2 hours) for better reliability
    if (minutesSinceStart >= VALIDATION_CONFIG.INITIAL_DELAY_MINUTES[0] && 
        minutesSinceStart <= 120) {
      return {type: 'initial'};
    }
  }

  // Daily samples
  const currentDay = daysSinceStart + 1;
  
  if (currentDay >= 1 && currentDay <= VALIDATION_CONFIG.TOTAL_DAYS) {
    const dayKey = `day${currentDay}`;
    const dailyAttempts = validation.dailyAttempts[dayKey] || 0;

    if (dailyAttempts < VALIDATION_CONFIG.SAMPLES_PER_DAY) {
      return {type: 'daily', day: currentDay};
    }
  }

  return null;
}

/**
 * Gets user location with validation
 */
async function getUserLocation(
  firestore: FirebaseFirestore.Firestore,
  userId: string,
  now: Date
): Promise<UserLocation | null> {
  const userLocationDoc = await firestore
    .collection("user_locations")
    .doc(userId)
    .get();

  if (!userLocationDoc.exists) {
    return null;
  }

  const userLocation = userLocationDoc.data() as UserLocation;
  
  if (!userLocation?.latitude || !userLocation?.longitude) {
    return null;
  }

  // Check if location is recent
  const locationTime = userLocation.timestamp?.toDate();
  const maxAge = VALIDATION_CONFIG.LOCATION_FRESHNESS_MINUTES * 60 * 1000;
  
  if (!locationTime || (now.getTime() - locationTime.getTime()) > maxAge) {
    return null;
  }

  return userLocation;
}

/**
 * Updates zone with sample result
 */
async function updateZoneWithSampleResult(
  firestore: FirebaseFirestore.Firestore,
  zoneId: string,
  type: SampleType,
  day: number | undefined,
  result: 'validated' | 'not_in_zone',
  now: Date,
  distance: number,
  radius: number
): Promise<void> {
  const updateData: Record<string, any> = {
    'automaticValidation.attempts': FieldValue.increment(1),
    'automaticValidation.lastAttempt': Timestamp.fromDate(now),
  };

  if (type === 'initial') {
    updateData['automaticValidation.initialSampleCompleted'] = true;
  }

  if (type === 'daily' && day) {
    updateData[`automaticValidation.dailyAttempts.day${day}`] = FieldValue.increment(1);
  }

  if (result === 'validated') {
    updateData['validationStatus'] = 'validated';
    updateData['validatedAt'] = Timestamp.fromDate(now);
  }

  await firestore.collection("zones").doc(zoneId).update(updateData);
}

/**
 * Calculates initial delay for first sample
 */
function calculateInitialDelay(): number {
  const [min, max] = VALIDATION_CONFIG.INITIAL_DELAY_MINUTES;
  return min + Math.random() * (max - min);
}

/**
 * Checks if current time is within presence hours
 */
function isWithinPresenceHours(config: PresenceHoursConfig | undefined, currentTime: Date): boolean {
  if (!config?.isAutomaticValidationEnabled || !config.timeBlocks?.length) {
    return false;
  }

  const currentDay = getDayOfWeek(currentTime);
  const currentMinutes = currentTime.getHours() * 60 + currentTime.getMinutes();

  return config.timeBlocks.some(block => {
    if (!block.isEnabled || !block.activeDays.includes(currentDay)) {
      return false;
    }

    const startMinutes = parseTimeToMinutes(block.startTime);
    const endMinutes = parseTimeToMinutes(block.endTime);

    // Handle overnight ranges
    if (startMinutes <= endMinutes) {
      return currentMinutes >= startMinutes && currentMinutes <= endMinutes;
    } else {
      return currentMinutes >= startMinutes || currentMinutes <= endMinutes;
    }
  });
}

/**
 * Gets day of week as lowercase string
 */
function getDayOfWeek(date: Date): string {
  const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
  return days[date.getDay()];
}

/**
 * Parses time string to minutes since midnight
 */
function parseTimeToMinutes(timeString: string): number {
  const [hours, minutes] = timeString.split(':').map(Number);
  return hours * 60 + minutes;
}
