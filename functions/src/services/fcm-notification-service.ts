import * as admin from 'firebase-admin';
import * as _ from 'lodash';

const messaging = admin.messaging();
const db = admin.firestore();

export interface NotificationPayload {
  title: string;
  body: string;
  data: Record<string, string>;
  imageUrl?: string;
  actionUrl?: string;
}

export interface IncidentNotificationData {
  incidentId: string;
  incidentType: string;
  severity: string;
  distance: string;
  latitude: string;
  longitude: string;
  timestamp: string;
  address: string;
}

export interface ValidationNotificationData {
  zoneId: string;
  zoneName: string;
  validationResult: 'validated' | 'not_in_zone' | 'no_location';
  sampleType: 'initial' | 'daily';
  distance: number;
  radiusInMeters: number;
  timestamp: string;
  attempt: number;
  totalAttempts: number;
}

export interface NotificationResult {
  userId: string;
  success: boolean;
  messageId?: string;
  error?: string;
  errorCode?: string;
}

export interface BatchNotificationResult {
  totalSent: number;
  totalFailed: number;
  failureReasons: { [reason: string]: number };
}

/**
 * Advanced FCM notification service with platform-specific optimizations
 */
export class FCMNotificationService {

  /**
   * Send automatic validation result notification to user
   */
  static async sendValidationResultNotification(
    userId: string,
    fcmToken: string,
    validationData: ValidationNotificationData
  ): Promise<NotificationResult> {
    try {
      const payload = this.createValidationPayload(validationData);
      const message = this.buildFCMMessage(fcmToken, payload);

      const response = await messaging.send(message);
      
      console.log(`✅ Validation notification sent to user ${userId}:`, response);

      // Log successful delivery
      await this.logValidationNotificationDelivery(
        userId, 
        validationData.zoneId, 
        'sent', 
        validationData.validationResult
      );

      return {
        userId,
        success: true,
        messageId: response
      };

    } catch (error: any) {
      console.error(`❌ Failed to send validation notification to user ${userId}:`, error);

      // Handle specific FCM errors
      const errorCode = this.getErrorCode(error);
      const errorMessage = this.getErrorMessage(error);

      // Log failed delivery
      await this.logValidationNotificationDelivery(
        userId, 
        validationData.zoneId, 
        'failed', 
        validationData.validationResult, 
        errorMessage
      );

      // Handle invalid tokens
      if (this.isInvalidTokenError(error)) {
        await this.handleInvalidToken(userId, fcmToken);
      }

      return {
        userId,
        success: false,
        error: errorMessage,
        errorCode
      };
    }
  }

  /**
   * Send incident notification to a single user
   */
  static async sendIncidentNotification(
    userId: string,
    fcmToken: string,
    incidentData: IncidentNotificationData,
    distanceText: string
  ): Promise<NotificationResult> {
    try {
      const payload = this.createIncidentPayload(incidentData, distanceText);
      const message = this.buildFCMMessage(fcmToken, payload);

      const response = await messaging.send(message);
      
      console.log(`✅ Notification sent to user ${userId}:`, response);

      // Log successful delivery
      await this.logNotificationDelivery(userId, incidentData.incidentId, 'sent', distanceText);

      return {
        userId,
        success: true,
        messageId: response
      };

    } catch (error: any) {
      console.error(`❌ Failed to send notification to user ${userId}:`, error);

      // Handle specific FCM errors
      const errorCode = this.getErrorCode(error);
      const errorMessage = this.getErrorMessage(error);

      // Log failed delivery
      await this.logNotificationDelivery(userId, incidentData.incidentId, 'failed', distanceText, errorMessage);

      // Handle invalid tokens
      if (this.isInvalidTokenError(error)) {
        await this.handleInvalidToken(userId, fcmToken);
      }

      return {
        userId,
        success: false,
        error: errorMessage,
        errorCode
      };
    }
  }

  /**
   * Send notifications to multiple users in batches
   */
  static async sendBatchIncidentNotifications(
    recipients: Array<{
      userId: string;
      fcmToken: string;
      distanceText: string;
    }>,
    incidentData: IncidentNotificationData
  ): Promise<BatchNotificationResult> {
    const results: NotificationResult[] = [];
    const failureReasons: { [reason: string]: number } = {};

    // Process in batches of 500 (FCM limit)
    const batchSize = 500;
    const batches = _.chunk(recipients, batchSize);

    console.log(`📱 Sending notifications to ${recipients.length} users in ${batches.length} batches`);

    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      console.log(`📤 Processing batch ${i + 1}/${batches.length} (${batch.length} recipients)`);

      // Create messages for this batch
      const messages = batch.map(recipient => {
        const payload = this.createIncidentPayload(incidentData, recipient.distanceText);
        return {
          ...this.buildFCMMessage(recipient.fcmToken, payload),
          token: recipient.fcmToken // Ensure token is set for batch sending
        };
      });

      try {
        // Send batch using sendAll
        const batchResponse = await messaging.sendAll(messages);
        
        console.log(`📊 Batch ${i + 1} results: ${batchResponse.successCount} sent, ${batchResponse.failureCount} failed`);

        // Process individual results
        for (let j = 0; j < batch.length; j++) {
          const recipient = batch[j];
          const response = batchResponse.responses[j];

          if (response.success) {
            results.push({
              userId: recipient.userId,
              success: true,
              messageId: response.messageId
            });

            // Log successful delivery
            await this.logNotificationDelivery(
              recipient.userId, 
              incidentData.incidentId, 
              'sent', 
              recipient.distanceText
            );

          } else {
            const error = response.error!;
            const errorCode = this.getErrorCode(error);
            const errorMessage = this.getErrorMessage(error);

            results.push({
              userId: recipient.userId,
              success: false,
              error: errorMessage,
              errorCode
            });

            // Track failure reasons
            failureReasons[errorCode] = (failureReasons[errorCode] || 0) + 1;

            // Log failed delivery
            await this.logNotificationDelivery(
              recipient.userId, 
              incidentData.incidentId, 
              'failed', 
              recipient.distanceText, 
              errorMessage
            );

            // Handle invalid tokens
            if (this.isInvalidTokenError(error)) {
              await this.handleInvalidToken(recipient.userId, recipient.fcmToken);
            }
          }
        }

      } catch (batchError) {
        console.error(`❌ Batch ${i + 1} failed completely:`, batchError);
        
        // Mark all recipients in this batch as failed
        for (const recipient of batch) {
          results.push({
            userId: recipient.userId,
            success: false,
            error: 'Batch send failed',
            errorCode: 'BATCH_FAILURE'
          });
        }

        failureReasons['BATCH_FAILURE'] = (failureReasons['BATCH_FAILURE'] || 0) + batch.length;
      }

      // Small delay between batches to avoid rate limiting
      if (i < batches.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    const totalSent = results.filter(r => r.success).length;
    const totalFailed = results.filter(r => !r.success).length;

    console.log(`📊 Final results: ${totalSent} sent, ${totalFailed} failed`);
    console.log('📊 Failure reasons:', failureReasons);

    return {
      totalSent,
      totalFailed,
      failureReasons
    };
  }

  /**
   * Create validation notification payload
   */
  private static createValidationPayload(validationData: ValidationNotificationData): NotificationPayload {
    const { validationResult } = validationData;
    
    if (validationResult === 'no_location') {
      // Location request notification
      const progressText = `Sample ${validationData.attempt}/${validationData.totalAttempts}`;
      
      return {
        title: '📍 Location Access Needed',
        body: `${validationData.zoneName} - Please enable location access for zone validation • ${progressText}`,
        data: {
          type: 'zone_location_request',
          zoneId: validationData.zoneId,
          zoneName: validationData.zoneName,
          validationResult: validationData.validationResult,
          sampleType: validationData.sampleType,
          timestamp: validationData.timestamp,
          attempt: validationData.attempt.toString(),
          totalAttempts: validationData.totalAttempts.toString(),
          clickAction: 'ZONE_LOCATION_REQUEST'
        }
      };
    }
    
    // Regular validation result notifications
    const isValidated = validationResult === 'validated';
    const distanceText = this.formatDistance(validationData.distance);
    
    const title = isValidated 
      ? '✅ Zone Validation Successful'
      : '❌ Zone Validation Failed';
    
    const body = isValidated
      ? `${validationData.zoneName} - You were detected within the zone (${distanceText})`
      : `${validationData.zoneName} - You were ${distanceText} from the zone center`;

    const progressText = `Sample ${validationData.attempt}/${validationData.totalAttempts}`;

    return {
      title,
      body: `${body} • ${progressText}`,
      data: {
        type: 'zone_validation_result',
        zoneId: validationData.zoneId,
        zoneName: validationData.zoneName,
        validationResult: validationData.validationResult,
        sampleType: validationData.sampleType,
        distance: validationData.distance.toString(),
        radiusInMeters: validationData.radiusInMeters.toString(),
        timestamp: validationData.timestamp,
        attempt: validationData.attempt.toString(),
        totalAttempts: validationData.totalAttempts.toString(),
        clickAction: 'ZONE_VALIDATION_DETAILS'
      }
    };
  }

  /**
   * Create incident notification payload
   */
  private static createIncidentPayload(
    incidentData: IncidentNotificationData,
    distanceText: string
  ): NotificationPayload {
    const title = this.getNotificationTitle(incidentData.incidentType, incidentData.severity);
    
    return {
      title,
      body: `${distanceText} - ${incidentData.address}`,
      data: {
        type: 'incident',
        incidentId: incidentData.incidentId,
        incidentType: incidentData.incidentType,
        severity: incidentData.severity,
        latitude: incidentData.latitude,
        longitude: incidentData.longitude,
        timestamp: incidentData.timestamp,
        address: incidentData.address,
        distance: incidentData.distance,
        clickAction: 'INCIDENT_DETAILS'
      }
    };
  }

  /**
   * Format distance in human-readable format
   */
  private static formatDistance(distanceInMeters: number): string {
    if (distanceInMeters < 1000) {
      return `${Math.round(distanceInMeters)}m away`;
    } else {
      return `${(distanceInMeters / 1000).toFixed(1)}km away`;
    }
  }

  /**
   * Build platform-optimized FCM message
   */
  private static buildFCMMessage(fcmToken: string, payload: NotificationPayload): admin.messaging.Message {
    return {
      token: fcmToken,
      notification: {
        title: payload.title,
        body: payload.body,
        imageUrl: payload.imageUrl
      },
      data: payload.data,
      android: {
        priority: 'high',
        notification: {
          channelId: 'security_alerts',
          priority: 'high',
          defaultSound: true,
          defaultVibrateTimings: true,
          color: '#FF5722', // Security alert color
          icon: 'ic_security_alert',
          tag: payload.data.incidentId, // Group notifications by incident
          clickAction: payload.data.clickAction || 'FLUTTER_NOTIFICATION_CLICK'
        },
        ttl: 3600000, // 1 hour TTL
        restrictedPackageName: 'com.pego.respublicaseguridad.respublicaseguridad'
      },
      apns: {
        headers: {
          'apns-priority': '10', // High priority
          'apns-expiration': String(Math.floor(Date.now() / 1000) + 3600) // 1 hour expiration
        },
        payload: {
          aps: {
            alert: {
              title: payload.title,
              body: payload.body
            },
            sound: 'default',
            badge: 1,
            category: 'SECURITY_ALERT',
            'thread-id': payload.data.incidentId, // Group notifications
            'mutable-content': 1 // Enable notification extensions
          },
          customData: payload.data
        }
      },
      webpush: {
        headers: {
          'TTL': '3600' // 1 hour TTL
        },
        notification: {
          title: payload.title,
          body: payload.body,
          icon: '/icons/security-alert.png',
          badge: '/icons/badge.png',
          tag: payload.data.incidentId,
          requireInteraction: true, // Keep notification visible
          actions: [
            {
              action: 'view',
              title: 'View Details'
            },
            {
              action: 'dismiss',
              title: 'Dismiss'
            }
          ]
        },
        data: payload.data
      }
    };
  }

  /**
   * Generate notification title based on incident type and severity
   */
  private static getNotificationTitle(incidentType: string, severity: string): string {
    const severityEmojis = {
      low: '⚠️',
      medium: '🚨',
      high: '🚨',
      critical: '🆘'
    };

    const typeLabels: Record<string, string> = {
      'theft': 'Theft Alert',
      'assault': 'Security Alert',
      'vandalism': 'Vandalism Report',
      'suspicious_activity': 'Suspicious Activity',
      'emergency': 'Emergency Alert',
      'fire': 'Fire Alert',
      'medical_emergency': 'Medical Emergency',
      'accident': 'Accident Report',
      'harassment': 'Harassment Report',
      'drug_activity': 'Drug Activity Report',
      'noise_complaint': 'Noise Complaint',
      'other': 'Security Alert'
    };

    const emoji = severityEmojis[severity as keyof typeof severityEmojis] || '🚨';
    const label = typeLabels[incidentType] || 'Security Alert';
    
    return `${emoji} ${label}`;
  }

  /**
   * Extract error code from FCM error
   */
  private static getErrorCode(error: any): string {
    if (error.code) return error.code;
    if (error.errorInfo?.code) return error.errorInfo.code;
    if (error.message?.includes('invalid-registration-token')) return 'invalid-registration-token';
    if (error.message?.includes('registration-token-not-registered')) return 'registration-token-not-registered';
    return 'unknown-error';
  }

  /**
   * Extract user-friendly error message
   */
  private static getErrorMessage(error: any): string {
    const errorCode = this.getErrorCode(error);
    
    const errorMessages: Record<string, string> = {
      'invalid-registration-token': 'Invalid FCM token',
      'registration-token-not-registered': 'FCM token not registered',
      'message-rate-exceeded': 'Message rate exceeded',
      'device-message-rate-exceeded': 'Device message rate exceeded',
      'topics-message-rate-exceeded': 'Topics message rate exceeded',
      'invalid-package-name': 'Invalid package name',
      'invalid-apns-credentials': 'Invalid APNS credentials',
      'unavailable': 'FCM service unavailable',
      'internal-error': 'Internal FCM error'
    };

    return errorMessages[errorCode] || error.message || 'Unknown error';
  }

  /**
   * Check if error indicates invalid token
   */
  private static isInvalidTokenError(error: any): boolean {
    const errorCode = this.getErrorCode(error);
    return ['invalid-registration-token', 'registration-token-not-registered'].includes(errorCode);
  }

  /**
   * Handle invalid FCM token by removing it from user preferences
   */
  private static async handleInvalidToken(userId: string, invalidToken: string): Promise<void> {
    try {
      await db.collection('notification_preferences').doc(userId).update({
        fcmToken: admin.firestore.FieldValue.delete(),
        invalidTokens: admin.firestore.FieldValue.arrayUnion(invalidToken),
        lastTokenInvalidated: admin.firestore.FieldValue.serverTimestamp()
      });

      console.log(`🗑️ Removed invalid FCM token for user ${userId}`);
    } catch (error) {
      console.error(`Error handling invalid token for user ${userId}:`, error);
    }
  }

  /**
   * Log notification delivery for analytics
   */
  private static async logNotificationDelivery(
    userId: string,
    incidentId: string,
    status: 'sent' | 'failed',
    distance: string,
    error?: string
  ): Promise<void> {
    try {
      await db.collection('notification_logs').add({
        userId,
        incidentId,
        status,
        distance,
        error: error || null,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        platform: 'fcm'
      });
    } catch (logError) {
      console.error('Failed to log notification delivery:', logError);
      // Don't throw here to avoid breaking the main flow
    }
  }

  /**
   * Log validation notification delivery
   */
  private static async logValidationNotificationDelivery(
    userId: string,
    zoneId: string,
    status: 'sent' | 'failed',
    validationResult: string,
    errorMessage?: string
  ): Promise<void> {
    try {
      await db.collection('notification_logs').add({
        type: 'zone_validation',
        userId,
        zoneId,
        validationResult,
        status,
        errorMessage: errorMessage || null,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
      });
    } catch (error) {
      console.error('Failed to log validation notification delivery:', error);
    }
  }

  /**
   * Send test notification to verify FCM setup
   */
  static async sendTestNotification(userId: string, fcmToken: string): Promise<NotificationResult> {
    const testData: IncidentNotificationData = {
      incidentId: 'test-' + Date.now(),
      incidentType: 'test',
      severity: 'medium',
      distance: '100m away',
      latitude: '0',
      longitude: '0',
      timestamp: new Date().toISOString(),
      address: 'Test Location'
    };

    return this.sendIncidentNotification(userId, fcmToken, testData, '100m away');
  }
}
